// Application principale
class CourseApp {
    constructor() {
        this.currentLanguage = 'fr';
        this.currentTheme = 'light';
        this.currentModule = null;
        this.progress = {
            completed: [],
            current: 'algo-intro',
            totalModules: 10
        };
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadProgress();
        this.updateLanguage();
        this.updateProgress();
        this.setupMobileMenu();
    }

    setupEventListeners() {
        // Language toggle
        document.getElementById('langToggle').addEventListener('click', () => {
            this.toggleLanguage();
        });

        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Mobile menu toggle
        document.getElementById('mobileMenuToggle').addEventListener('click', () => {
            this.toggleMobileMenu();
        });

        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('href').substring(1);
                this.navigateToSection(section);
            });
        });

        // Module items
        document.querySelectorAll('.module-item').forEach(item => {
            item.addEventListener('click', () => {
                const moduleId = item.getAttribute('data-module');
                this.loadModule(moduleId);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'l':
                        e.preventDefault();
                        this.toggleLanguage();
                        break;
                    case 'd':
                        e.preventDefault();
                        this.toggleTheme();
                        break;
                }
            }
        });
    }

    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'fr' ? 'ar' : 'fr';
        this.updateLanguage();
        this.saveSettings();
    }

    updateLanguage() {
        const html = document.documentElement;
        const langToggle = document.getElementById('currentLang');
        
        if (this.currentLanguage === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            langToggle.textContent = 'ع';
        } else {
            html.setAttribute('lang', 'fr');
            html.setAttribute('dir', 'ltr');
            langToggle.textContent = 'FR';
        }

        // Update all translatable elements
        document.querySelectorAll('[data-fr][data-ar]').forEach(element => {
            const text = element.getAttribute(`data-${this.currentLanguage}`);
            if (text) {
                element.textContent = text;
            }
        });
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        
        const themeIcon = document.querySelector('#themeToggle i');
        themeIcon.className = this.currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        
        this.saveSettings();
    }

    toggleMobileMenu() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('open');
    }

    setupMobileMenu() {
        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            const sidebar = document.getElementById('sidebar');
            const mobileToggle = document.getElementById('mobileMenuToggle');
            
            if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
                sidebar.classList.remove('open');
            }
        });
    }

    navigateToSection(sectionId) {
        // Hide all sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[href="#${sectionId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // Close mobile menu
        document.getElementById('sidebar').classList.remove('open');
    }

    loadModule(moduleId) {
        if (!this.isModuleUnlocked(moduleId)) {
            this.showNotification(
                this.currentLanguage === 'fr' 
                    ? 'Ce module n\'est pas encore débloqué' 
                    : 'هذه الوحدة غير متاحة بعد',
                'warning'
            );
            return;
        }

        this.currentModule = moduleId;
        this.updateModuleStates();
        
        // Load module content
        if (window.ModuleManager) {
            window.ModuleManager.loadModule(moduleId);
        }
        
        // Navigate to module content
        this.navigateToSection('module-content');
    }

    isModuleUnlocked(moduleId) {
        const modules = [
            'algo-intro', 'algo-structures', 'algo-sort', 'algo-recursion', 'algo-graphs',
            'python-basics', 'python-structures', 'python-oop', 'python-modules', 'python-projects'
        ];
        
        const moduleIndex = modules.indexOf(moduleId);
        const completedCount = this.progress.completed.length;
        
        return moduleIndex <= completedCount;
    }

    updateModuleStates() {
        document.querySelectorAll('.module-item').forEach(item => {
            const moduleId = item.getAttribute('data-module');
            const icon = item.querySelector('i');
            
            item.classList.remove('completed', 'current');
            
            if (this.progress.completed.includes(moduleId)) {
                item.classList.add('completed');
                icon.className = 'fas fa-check-circle';
            } else if (moduleId === this.currentModule) {
                item.classList.add('current');
                icon.className = 'fas fa-play-circle';
            } else if (this.isModuleUnlocked(moduleId)) {
                icon.className = 'fas fa-play-circle';
            } else {
                icon.className = 'fas fa-lock';
            }
        });
    }

    completeModule(moduleId) {
        if (!this.progress.completed.includes(moduleId)) {
            this.progress.completed.push(moduleId);
            this.updateProgress();
            this.updateModuleStates();
            this.saveProgress();
            
            this.showNotification(
                this.currentLanguage === 'fr' 
                    ? 'Module terminé avec succès !' 
                    : 'تم إنهاء الوحدة بنجاح!',
                'success'
            );
        }
    }

    updateProgress() {
        const progressPercentage = Math.round((this.progress.completed.length / this.progress.totalModules) * 100);
        const progressText = document.querySelector('.progress-text');
        const progressCircle = document.querySelector('.progress-ring-circle');
        
        if (progressText) {
            progressText.textContent = `${progressPercentage}%`;
        }
        
        if (progressCircle) {
            const circumference = 2 * Math.PI * 30; // radius = 30
            const offset = circumference - (progressPercentage / 100) * circumference;
            progressCircle.style.strokeDashoffset = offset;
            progressCircle.style.stroke = progressPercentage > 0 ? 'var(--primary-color)' : 'var(--border-color)';
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add styles
        Object.assign(notification.style, {
            position: 'fixed',
            top: '5rem',
            right: '1rem',
            background: type === 'success' ? 'var(--secondary-color)' : 
                       type === 'warning' ? 'var(--accent-color)' : 'var(--primary-color)',
            color: 'white',
            padding: '1rem 1.5rem',
            borderRadius: 'var(--border-radius)',
            boxShadow: 'var(--shadow-lg)',
            zIndex: '3000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    saveSettings() {
        localStorage.setItem('courseSettings', JSON.stringify({
            language: this.currentLanguage,
            theme: this.currentTheme
        }));
    }

    loadSettings() {
        const settings = localStorage.getItem('courseSettings');
        if (settings) {
            const parsed = JSON.parse(settings);
            this.currentLanguage = parsed.language || 'fr';
            this.currentTheme = parsed.theme || 'light';
            
            document.documentElement.setAttribute('data-theme', this.currentTheme);
        }
    }

    saveProgress() {
        localStorage.setItem('courseProgress', JSON.stringify(this.progress));
    }

    loadProgress() {
        const progress = localStorage.getItem('courseProgress');
        if (progress) {
            this.progress = { ...this.progress, ...JSON.parse(progress) };
        }
    }
}

// Global functions
function startCourse() {
    if (window.app) {
        window.app.loadModule('algo-intro');
    }
}

function closeCodeEditor() {
    const modal = document.getElementById('codeModal');
    modal.classList.remove('active');
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new CourseApp();
});
