// Application principale - Version créative avec tunisien
class AlgorithmicApp {
    constructor() {
        this.currentLang = 'fr';
        this.currentTheme = 'light';
        this.currentSection = 'home';
        this.userProgress = this.loadProgress();
        this.init();
    }

    init() {
        this.initParticles();
        this.initEventListeners();
        this.initAnimations();
        this.loadUserPreferences();
        this.updateLanguage();
        this.showSection('home');
    }

    // Initialisation des particules de fond
    initParticles() {
        if (typeof particlesJS !== 'undefined') {
            particlesJS('particles-js', {
                particles: {
                    number: { value: 80, density: { enable: true, value_area: 800 } },
                    color: { value: '#667eea' },
                    shape: { type: 'circle' },
                    opacity: { value: 0.5, random: false },
                    size: { value: 3, random: true },
                    line_linked: {
                        enable: true,
                        distance: 150,
                        color: '#667eea',
                        opacity: 0.4,
                        width: 1
                    },
                    move: {
                        enable: true,
                        speed: 2,
                        direction: 'none',
                        random: false,
                        straight: false,
                        out_mode: 'out',
                        bounce: false
                    }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: {
                        onhover: { enable: true, mode: 'repulse' },
                        onclick: { enable: true, mode: 'push' },
                        resize: true
                    }
                },
                retina_detect: true
            });
        }
    }

    // Initialisation des événements
    initEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('href').substring(1);
                this.showSection(section);
            });
        });

        // Changement de langue
        document.getElementById('langToggle').addEventListener('click', () => {
            this.toggleLanguage();
        });

        // Options de langue
        document.querySelectorAll('.lang-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const lang = e.target.getAttribute('data-lang');
                this.setLanguage(lang);
            });
        });

        // Changement de thème
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Menu mobile
        document.getElementById('mobileMenuToggle').addEventListener('click', () => {
            this.toggleMobileMenu();
        });

        // FAB
        document.getElementById('helpFab').addEventListener('click', () => {
            this.showHelp();
        });

        // Boutons hero
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick="startLearning()"]')) {
                this.startLearning();
            }
            if (e.target.closest('[onclick="showDemo()"]')) {
                this.showDemo();
            }
        });

        // Fermeture des modals
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });
    }

    // Initialisation des animations
    initAnimations() {
        // Animation des barres de tri
        this.animateSortingBars();
        
        // Animation des cartes au scroll
        this.initScrollAnimations();
        
        // Animation des statistiques
        this.animateStats();
    }

    // Animation des barres de tri
    animateSortingBars() {
        const bars = document.querySelectorAll('.bar');
        if (bars.length === 0) return;

        setInterval(() => {
            bars.forEach((bar, index) => {
                const heights = ['20%', '60%', '40%', '80%', '30%', '70%', '50%', '90%'];
                setTimeout(() => {
                    bar.style.height = heights[Math.floor(Math.random() * heights.length)];
                }, index * 100);
            });
        }, 3000);
    }

    // Animations au scroll
    initScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.getAttribute('data-delay') || '0s';
                    entry.target.style.animationDelay = delay;
                    entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.feature-card, .stat-card').forEach(el => {
            observer.observe(el);
        });
    }

    // Animation des statistiques
    animateStats() {
        const statNumbers = document.querySelectorAll('.stat-number');
        statNumbers.forEach(stat => {
            const finalValue = parseInt(stat.textContent);
            let currentValue = 0;
            const increment = finalValue / 50;
            
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    currentValue = finalValue;
                    clearInterval(timer);
                }
                stat.textContent = Math.floor(currentValue);
            }, 50);
        });
    }

    // Gestion des langues
    toggleLanguage() {
        this.currentLang = this.currentLang === 'fr' ? 'tn' : 'fr';
        this.setLanguage(this.currentLang);
    }

    setLanguage(lang) {
        this.currentLang = lang;
        this.updateLanguage();
        this.savePreferences();
    }

    updateLanguage() {
        const elements = document.querySelectorAll('[data-fr][data-tn]');
        elements.forEach(el => {
            const text = el.getAttribute(`data-${this.currentLang}`);
            if (text) {
                if (el.tagName === 'INPUT' || el.tagName === 'TEXTAREA') {
                    el.placeholder = text;
                } else {
                    el.textContent = text;
                }
            }
        });

        // Mise à jour de la direction et langue
        document.documentElement.lang = this.currentLang;
        document.documentElement.dir = this.currentLang === 'tn' ? 'rtl' : 'ltr';
        
        // Mise à jour du bouton de langue
        document.getElementById('currentLang').textContent = this.currentLang.toUpperCase();
    }

    // Gestion des thèmes
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(this.currentTheme);
    }

    setTheme(theme) {
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        
        const themeIcon = document.querySelector('#themeToggle i');
        themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        
        this.savePreferences();
    }

    // Navigation entre sections
    showSection(sectionId) {
        // Masquer toutes les sections
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
        });

        // Afficher la section demandée
        const targetSection = document.getElementById(sectionId);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionId;
        }

        // Mettre à jour la navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[href="#${sectionId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        // Charger le contenu spécifique
        this.loadSectionContent(sectionId);
    }

    // Chargement du contenu des sections
    loadSectionContent(sectionId) {
        switch(sectionId) {
            case 'learn':
                this.loadLearningContent();
                break;
            case 'games':
                this.loadGamesContent();
                break;
            case 'quiz':
                this.loadQuizContent();
                break;
            case 'progress':
                this.loadProgressContent();
                break;
        }
    }

    // Actions principales
    startLearning() {
        this.showSection('learn');
        this.trackEvent('start_learning');
    }

    showDemo() {
        // Afficher une démo interactive
        this.showModal('gameModal', 'Démo Interactive', this.createDemoContent());
    }

    showHelp() {
        const helpContent = this.currentLang === 'fr' 
            ? 'Bienvenue dans votre plateforme d\'apprentissage ! Utilisez la navigation pour explorer les différentes sections.'
            : 'أهلا بيك في منصة التعلم متاعك! استعمل التنقل باش تكتشف الأقسام المختلفة.';
        
        alert(helpContent);
    }

    // Gestion des modals
    showModal(modalId, title, content) {
        const modal = document.getElementById(modalId);
        const titleEl = modal.querySelector('h3');
        const contentEl = modal.querySelector('.modal-body');
        
        if (titleEl) titleEl.textContent = title;
        if (contentEl) contentEl.innerHTML = content;
        
        modal.classList.add('active');
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.classList.remove('active');
    }

    // Sauvegarde et chargement
    savePreferences() {
        const preferences = {
            lang: this.currentLang,
            theme: this.currentTheme,
            progress: this.userProgress
        };
        localStorage.setItem('algorithmicApp_preferences', JSON.stringify(preferences));
    }

    loadUserPreferences() {
        const saved = localStorage.getItem('algorithmicApp_preferences');
        if (saved) {
            const preferences = JSON.parse(saved);
            this.currentLang = preferences.lang || 'fr';
            this.currentTheme = preferences.theme || 'light';
            this.userProgress = preferences.progress || {};
            
            this.setTheme(this.currentTheme);
        }
    }

    loadProgress() {
        const saved = localStorage.getItem('algorithmicApp_progress');
        return saved ? JSON.parse(saved) : {
            completedLessons: [],
            gameScores: {},
            quizScores: {},
            totalTime: 0
        };
    }

    saveProgress() {
        localStorage.setItem('algorithmicApp_progress', JSON.stringify(this.userProgress));
    }

    // Suivi des événements
    trackEvent(eventName, data = {}) {
        console.log(`Event: ${eventName}`, data);
        // Ici on pourrait ajouter Google Analytics ou autre
    }
}

// Fonctions globales pour la compatibilité
window.startLearning = () => app.startLearning();
window.showDemo = () => app.showDemo();
window.closeModal = (modalId) => app.closeModal(modalId);

// Initialisation de l'application
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new AlgorithmicApp();
});
