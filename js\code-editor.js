// Éditeur de code intégré
class CodeEditor {
    constructor() {
        this.isOpen = false;
        this.currentLanguage = 'python';
        this.code = '';
    }

    open(initialCode = '', language = 'python') {
        this.currentLanguage = language;
        this.code = initialCode;
        
        const modal = document.getElementById('codeModal');
        const editor = document.getElementById('codeEditor');
        const languageSelect = document.getElementById('languageSelect');
        
        editor.value = initialCode;
        languageSelect.value = language;
        
        modal.classList.add('active');
        this.isOpen = true;
        
        // Focus on editor
        setTimeout(() => {
            editor.focus();
        }, 300);
        
        // Setup syntax highlighting (basic)
        this.setupSyntaxHighlighting();
    }

    close() {
        const modal = document.getElementById('codeModal');
        modal.classList.remove('active');
        this.isOpen = false;
    }

    setupSyntaxHighlighting() {
        const editor = document.getElementById('codeEditor');
        
        // Basic syntax highlighting with CSS classes
        editor.addEventListener('input', () => {
            this.code = editor.value;
            // You could implement more sophisticated syntax highlighting here
        });
        
        // Add line numbers
        this.addLineNumbers();
        
        // Add keyboard shortcuts
        editor.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    addLineNumbers() {
        const editor = document.getElementById('codeEditor');
        const container = editor.parentElement;
        
        // Create line numbers container if it doesn't exist
        let lineNumbers = container.querySelector('.line-numbers');
        if (!lineNumbers) {
            lineNumbers = document.createElement('div');
            lineNumbers.className = 'line-numbers';
            container.insertBefore(lineNumbers, editor);
            
            // Style the container
            container.style.position = 'relative';
            container.style.display = 'flex';
            
            // Style line numbers
            lineNumbers.style.cssText = `
                background: var(--bg-tertiary);
                color: var(--text-light);
                padding: 1rem 0.5rem;
                font-family: 'Courier New', monospace;
                font-size: 14px;
                line-height: 1.5;
                border-right: 1px solid var(--border-color);
                user-select: none;
                min-width: 3rem;
                text-align: right;
            `;
            
            // Style editor
            editor.style.cssText += `
                flex: 1;
                border: none;
                outline: none;
                resize: none;
                font-family: 'Courier New', monospace;
                font-size: 14px;
                line-height: 1.5;
                padding: 1rem;
                background: var(--bg-primary);
                color: var(--text-primary);
            `;
        }
        
        // Update line numbers
        editor.addEventListener('input', () => {
            this.updateLineNumbers();
        });
        
        editor.addEventListener('scroll', () => {
            lineNumbers.scrollTop = editor.scrollTop;
        });
        
        this.updateLineNumbers();
    }

    updateLineNumbers() {
        const editor = document.getElementById('codeEditor');
        const lineNumbers = editor.parentElement.querySelector('.line-numbers');
        
        if (lineNumbers) {
            const lines = editor.value.split('\n').length;
            const numbers = Array.from({ length: lines }, (_, i) => i + 1).join('\n');
            lineNumbers.textContent = numbers;
        }
    }

    handleKeyboardShortcuts(e) {
        const editor = document.getElementById('codeEditor');
        
        // Tab for indentation
        if (e.key === 'Tab') {
            e.preventDefault();
            const start = editor.selectionStart;
            const end = editor.selectionEnd;
            
            // Insert 4 spaces
            const spaces = '    ';
            editor.value = editor.value.substring(0, start) + spaces + editor.value.substring(end);
            editor.selectionStart = editor.selectionEnd = start + spaces.length;
        }
        
        // Ctrl+Enter to run code
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            this.runCode();
        }
        
        // Auto-close brackets and quotes
        if (['(', '[', '{', '"', "'"].includes(e.key)) {
            this.autoCloseBrackets(e);
        }
    }

    autoCloseBrackets(e) {
        const editor = document.getElementById('codeEditor');
        const start = editor.selectionStart;
        const end = editor.selectionEnd;
        
        const pairs = {
            '(': ')',
            '[': ']',
            '{': '}',
            '"': '"',
            "'": "'"
        };
        
        const closing = pairs[e.key];
        if (closing && start === end) {
            setTimeout(() => {
                const currentPos = editor.selectionStart;
                editor.value = editor.value.substring(0, currentPos) + closing + editor.value.substring(currentPos);
                editor.selectionStart = editor.selectionEnd = currentPos;
            }, 0);
        }
    }

    runCode() {
        const editor = document.getElementById('codeEditor');
        const output = document.getElementById('codeOutput');
        const languageSelect = document.getElementById('languageSelect');
        
        const code = editor.value;
        const language = languageSelect.value;
        
        output.textContent = '';
        output.style.color = 'var(--text-primary)';
        
        try {
            if (language === 'javascript') {
                this.runJavaScript(code, output);
            } else if (language === 'python') {
                this.runPython(code, output);
            }
        } catch (error) {
            output.textContent = `Erreur: ${error.message}`;
            output.style.color = 'var(--accent-color)';
        }
    }

    runJavaScript(code, output) {
        // Capture console.log output
        const originalLog = console.log;
        let logs = [];
        
        console.log = (...args) => {
            logs.push(args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' '));
        };
        
        try {
            // Execute the code
            const result = eval(code);
            
            // Restore console.log
            console.log = originalLog;
            
            // Display output
            if (logs.length > 0) {
                output.textContent = logs.join('\n');
            } else if (result !== undefined) {
                output.textContent = String(result);
            } else {
                output.textContent = 'Code exécuté avec succès (aucune sortie)';
            }
        } catch (error) {
            console.log = originalLog;
            throw error;
        }
    }

    runPython(code, output) {
        // This is a mock Python execution
        // In a real application, you would use a Python interpreter like Pyodide or a backend service
        
        output.textContent = `Simulation d'exécution Python:
        
Code:
${code}

Note: Pour une vraie exécution Python, vous devriez intégrer Pyodide ou utiliser un service backend.`;
        
        // You could integrate Pyodide here for real Python execution:
        // if (window.pyodide) {
        //     try {
        //         const result = window.pyodide.runPython(code);
        //         output.textContent = result;
        //     } catch (error) {
        //         output.textContent = `Erreur Python: ${error.message}`;
        //         output.style.color = 'var(--accent-color)';
        //     }
        // }
    }

    resetCode() {
        const editor = document.getElementById('codeEditor');
        const output = document.getElementById('codeOutput');
        
        editor.value = '';
        output.textContent = '';
        
        this.updateLineNumbers();
    }

    insertTemplate(template) {
        const editor = document.getElementById('codeEditor');
        const templates = {
            'python-function': `def ma_fonction(parametre):
    """Description de la fonction"""
    # Votre code ici
    return resultat`,
            
            'python-class': `class MaClasse:
    def __init__(self, parametre):
        self.parametre = parametre
    
    def ma_methode(self):
        # Votre code ici
        pass`,
            
            'javascript-function': `function maFonction(parametre) {
    // Votre code ici
    return resultat;
}`,
            
            'javascript-class': `class MaClasse {
    constructor(parametre) {
        this.parametre = parametre;
    }
    
    maMethode() {
        // Votre code ici
    }
}`
        };
        
        if (templates[template]) {
            editor.value = templates[template];
            this.updateLineNumbers();
        }
    }
}

// Global functions for code editor
function runCode() {
    if (window.codeEditor) {
        window.codeEditor.runCode();
    }
}

function resetCode() {
    if (window.codeEditor) {
        window.codeEditor.resetCode();
    }
}

function closeCodeEditor() {
    if (window.codeEditor) {
        window.codeEditor.close();
    }
}

// Initialize code editor
document.addEventListener('DOMContentLoaded', () => {
    window.codeEditor = new CodeEditor();
    
    // Language change handler
    document.getElementById('languageSelect').addEventListener('change', (e) => {
        window.codeEditor.currentLanguage = e.target.value;
    });
});
