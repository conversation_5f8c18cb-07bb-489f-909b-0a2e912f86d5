/* Support RTL pour le tunisien */
[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

[dir="rtl"] body {
    font-family: '<PERSON><PERSON>', 'Cairo', '<PERSON><PERSON><PERSON>', serif;
}

/* Header RTL */
[dir="rtl"] .header-content {
    flex-direction: row-reverse;
}

[dir="rtl"] .logo {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-list {
    flex-direction: row-reverse;
}

[dir="rtl"] .header-actions {
    flex-direction: row-reverse;
}

/* Navigation RTL */
[dir="rtl"] .nav-link {
    flex-direction: row-reverse;
}

/* Hero Section RTL */
[dir="rtl"] .hero {
    grid-template-columns: 1fr 1fr;
    direction: rtl;
}

[dir="rtl"] .hero-content {
    text-align: right;
}

[dir="rtl"] .hero-actions {
    justify-content: flex-start;
    flex-direction: row-reverse;
}

[dir="rtl"] .hero-stats {
    flex-direction: row-reverse;
}

[dir="rtl"] .stat-card {
    flex-direction: row-reverse;
}

/* Features RTL */
[dir="rtl"] .features-grid .feature-card {
    text-align: right;
}

/* Games RTL */
[dir="rtl"] .games-grid .game-card {
    text-align: right;
}

[dir="rtl"] .game-stats {
    flex-direction: row-reverse;
}

/* Quiz RTL */
[dir="rtl"] .quiz-option {
    text-align: right;
}

/* Progress RTL */
[dir="rtl"] .progress-dashboard {
    direction: rtl;
}

/* Modal RTL */
[dir="rtl"] .modal-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-body {
    text-align: right;
}

/* FAB RTL */
[dir="rtl"] .fab-container {
    right: auto;
    left: var(--spacing-xl);
}

[dir="rtl"] .fab-menu {
    right: auto;
    left: 0;
}

/* Buttons RTL */
[dir="rtl"] .btn-primary,
[dir="rtl"] .btn-secondary {
    flex-direction: row-reverse;
}

/* Responsive RTL */
@media (max-width: 768px) {
    [dir="rtl"] .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    [dir="rtl"] .hero-stats {
        flex-direction: column;
    }
    
    [dir="rtl"] .fab-container {
        left: var(--spacing-lg);
    }
}

/* Polices tunisiennes spécifiques */
[lang="tn"] {
    font-family: 'Amiri', 'Cairo', 'Tajawal', serif;
    font-weight: 400;
    line-height: 1.8;
}

[lang="tn"] h1,
[lang="tn"] h2,
[lang="tn"] h3,
[lang="tn"] h4,
[lang="tn"] h5,
[lang="tn"] h6 {
    font-family: 'Amiri', 'Cairo', serif;
    font-weight: 700;
}

/* Ajustements pour le texte tunisien */
[lang="tn"] .hero-title {
    font-size: var(--text-4xl);
    line-height: 1.3;
}

[lang="tn"] .hero-subtitle {
    font-size: var(--text-lg);
    line-height: 1.7;
}

[lang="tn"] .feature-card h3 {
    font-size: var(--text-lg);
    line-height: 1.4;
}

[lang="tn"] .game-title {
    font-size: var(--text-lg);
    line-height: 1.4;
}

[lang="tn"] .quiz-question {
    font-size: var(--text-lg);
    line-height: 1.6;
}

/* Expressions tunisiennes - Styles spéciaux */
.tunisian-expression {
    font-style: italic;
    color: var(--accent-color);
    font-weight: 600;
}

.tunisian-greeting {
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Animations pour le texte tunisien */
[lang="tn"] .animate__animated {
    animation-duration: 0.8s;
}

[lang="tn"] .hero-title {
    animation-delay: 0.2s;
}

[lang="tn"] .hero-subtitle {
    animation-delay: 0.4s;
}

/* Code direction reste LTR même en RTL */
[dir="rtl"] .code-editor,
[dir="rtl"] .code-block,
[dir="rtl"] pre,
[dir="rtl"] code {
    direction: ltr;
    text-align: left;
}

/* Ajustements pour les icônes en RTL */
[dir="rtl"] .nav-link i {
    margin-left: var(--spacing-sm);
    margin-right: 0;
}

[dir="rtl"] .stat-icon {
    margin-left: var(--spacing-md);
    margin-right: 0;
}

[dir="rtl"] .feature-icon {
    margin-left: auto;
    margin-right: auto;
}

/* Ajustements pour les listes en RTL */
[dir="rtl"] ul,
[dir="rtl"] ol {
    padding-right: var(--spacing-lg);
    padding-left: 0;
}

[dir="rtl"] li {
    text-align: right;
}

/* Ajustements pour les formulaires en RTL */
[dir="rtl"] input,
[dir="rtl"] textarea,
[dir="rtl"] select {
    text-align: right;
}

[dir="rtl"] .quiz-options {
    text-align: right;
}

/* Ajustements pour les tooltips en RTL */
[dir="rtl"] .tooltip {
    direction: rtl;
    text-align: right;
}

/* Ajustements pour les badges en RTL */
[dir="rtl"] .badge,
[dir="rtl"] .tag {
    direction: rtl;
}

/* Ajustements pour les cartes en RTL */
[dir="rtl"] .card-header {
    text-align: right;
}

[dir="rtl"] .card-body {
    text-align: right;
}

[dir="rtl"] .card-footer {
    text-align: right;
    flex-direction: row-reverse;
}

/* Ajustements pour les alertes en RTL */
[dir="rtl"] .alert {
    text-align: right;
}

[dir="rtl"] .alert-icon {
    margin-left: var(--spacing-sm);
    margin-right: 0;
}

/* Ajustements pour les breadcrumbs en RTL */
[dir="rtl"] .breadcrumb {
    flex-direction: row-reverse;
}

[dir="rtl"] .breadcrumb-item::before {
    content: "\\";
    margin-left: var(--spacing-sm);
    margin-right: 0;
}

/* Ajustements pour les tabs en RTL */
[dir="rtl"] .tab-list {
    flex-direction: row-reverse;
}

[dir="rtl"] .tab-content {
    text-align: right;
}
