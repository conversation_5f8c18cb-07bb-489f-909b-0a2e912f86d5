/* RTL Support for Arabic */
[dir="rtl"] {
    font-family: '<PERSON><PERSON>', 'Inter', sans-serif;
}

[dir="rtl"] .header-content {
    flex-direction: row-reverse;
}

[dir="rtl"] .logo {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-list {
    flex-direction: row-reverse;
}

[dir="rtl"] .header-actions {
    flex-direction: row-reverse;
}

[dir="rtl"] .main {
    flex-direction: row-reverse;
}

[dir="rtl"] .sidebar {
    right: 0;
    left: auto;
    border-right: none;
    border-left: 1px solid var(--border-color);
}

[dir="rtl"] .content-area {
    margin-left: 0;
    margin-right: 300px;
}

[dir="rtl"] .module-item {
    flex-direction: row-reverse;
    text-align: right;
}

[dir="rtl"] .module-item i {
    margin-left: 0.75rem;
    margin-right: 0;
}

[dir="rtl"] .stats {
    flex-direction: row-reverse;
}

[dir="rtl"] .cta-button {
    flex-direction: row-reverse;
}

[dir="rtl"] .modal-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .editor-toolbar {
    flex-direction: row-reverse;
}

[dir="rtl"] .code-editor-container {
    text-align: right;
}

[dir="rtl"] #codeEditor {
    direction: ltr; /* Keep code direction LTR */
    text-align: left;
}

/* Arabic Typography */
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
    font-family: 'Amiri', serif;
    font-weight: 700;
}

[dir="rtl"] p,
[dir="rtl"] span,
[dir="rtl"] div {
    font-family: 'Amiri', sans-serif;
    line-height: 1.8;
}

/* Mobile RTL adjustments */
@media (max-width: 768px) {
    [dir="rtl"] .sidebar {
        transform: translateX(100%);
    }
    
    [dir="rtl"] .sidebar.open {
        transform: translateX(0);
    }
    
    [dir="rtl"] .content-area {
        margin-right: 0;
    }
}
