<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cours Algorithmique et Python - دورة الخوارزميات وبايثون</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/rtl.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-code"></i>
                    <span class="logo-text" data-fr="Cours Algo & Python" data-ar="دورة الخوارزميات وبايثون">Cours Algo & Python</span>
                </div>
                
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="#home" class="nav-link active" data-fr="Accueil" data-ar="الرئيسية">Accueil</a></li>
                        <li><a href="#algorithmique" class="nav-link" data-fr="Algorithmique" data-ar="الخوارزميات">Algorithmique</a></li>
                        <li><a href="#python" class="nav-link" data-fr="Python" data-ar="بايثون">Python</a></li>
                        <li><a href="#exercices" class="nav-link" data-fr="Exercices" data-ar="التمارين">Exercices</a></li>
                        <li><a href="#progression" class="nav-link" data-fr="Progression" data-ar="التقدم">Progression</a></li>
                    </ul>
                </nav>

                <div class="header-actions">
                    <button class="lang-toggle" id="langToggle">
                        <i class="fas fa-language"></i>
                        <span id="currentLang">FR</span>
                    </button>
                    <button class="theme-toggle" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <div class="progress-overview">
                    <h3 data-fr="Votre Progression" data-ar="تقدمك">Votre Progression</h3>
                    <div class="progress-circle">
                        <svg class="progress-ring" width="80" height="80">
                            <circle class="progress-ring-circle" cx="40" cy="40" r="30"></circle>
                        </svg>
                        <span class="progress-text">0%</span>
                    </div>
                </div>

                <div class="modules-list">
                    <div class="module-section">
                        <h4 data-fr="Algorithmique (10h)" data-ar="الخوارزميات (10 ساعات)">Algorithmique (10h)</h4>
                        <ul class="module-items">
                            <li class="module-item" data-module="algo-intro">
                                <i class="fas fa-play-circle"></i>
                                <span data-fr="Introduction aux algorithmes" data-ar="مقدمة في الخوارزميات">Introduction aux algorithmes</span>
                                <span class="duration">2h</span>
                            </li>
                            <li class="module-item" data-module="algo-structures">
                                <i class="fas fa-lock"></i>
                                <span data-fr="Structures de données" data-ar="هياكل البيانات">Structures de données</span>
                                <span class="duration">2h</span>
                            </li>
                            <li class="module-item" data-module="algo-sort">
                                <i class="fas fa-lock"></i>
                                <span data-fr="Tri et recherche" data-ar="الترتيب والبحث">Tri et recherche</span>
                                <span class="duration">2h</span>
                            </li>
                            <li class="module-item" data-module="algo-recursion">
                                <i class="fas fa-lock"></i>
                                <span data-fr="Récursivité" data-ar="العودية">Récursivité</span>
                                <span class="duration">2h</span>
                            </li>
                            <li class="module-item" data-module="algo-graphs">
                                <i class="fas fa-lock"></i>
                                <span data-fr="Graphes et arbres" data-ar="الرسوم والأشجار">Graphes et arbres</span>
                                <span class="duration">2h</span>
                            </li>
                        </ul>
                    </div>

                    <div class="module-section">
                        <h4 data-fr="Python (10h)" data-ar="بايثون (10 ساعات)">Python (10h)</h4>
                        <ul class="module-items">
                            <li class="module-item" data-module="python-basics">
                                <i class="fas fa-lock"></i>
                                <span data-fr="Bases de Python" data-ar="أساسيات بايثون">Bases de Python</span>
                                <span class="duration">2h</span>
                            </li>
                            <li class="module-item" data-module="python-structures">
                                <i class="fas fa-lock"></i>
                                <span data-fr="Structures Python" data-ar="هياكل بايثون">Structures Python</span>
                                <span class="duration">2h</span>
                            </li>
                            <li class="module-item" data-module="python-oop">
                                <i class="fas fa-lock"></i>
                                <span data-fr="POO" data-ar="البرمجة الكائنية">POO</span>
                                <span class="duration">2h</span>
                            </li>
                            <li class="module-item" data-module="python-modules">
                                <i class="fas fa-lock"></i>
                                <span data-fr="Modules" data-ar="الوحدات">Modules</span>
                                <span class="duration">2h</span>
                            </li>
                            <li class="module-item" data-module="python-projects">
                                <i class="fas fa-lock"></i>
                                <span data-fr="Projets" data-ar="المشاريع">Projets</span>
                                <span class="duration">2h</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Content Area -->
        <div class="content-area">
            <div class="content" id="content">
                <!-- Home Section -->
                <section id="home" class="section active">
                    <div class="hero">
                        <h1 data-fr="Formation Algorithmique et Python" data-ar="تكوين في الخوارزميات وبايثون">Formation Algorithmique et Python</h1>
                        <p data-fr="Maîtrisez les fondamentaux de l'algorithmique et de la programmation Python en 20 heures de formation interactive." data-ar="أتقن أساسيات الخوارزميات وبرمجة بايثون في 20 ساعة من التدريب التفاعلي.">Maîtrisez les fondamentaux de l'algorithmique et de la programmation Python en 20 heures de formation interactive.</p>
                        
                        <div class="stats">
                            <div class="stat-item">
                                <span class="stat-number">20</span>
                                <span class="stat-label" data-fr="Heures" data-ar="ساعة">Heures</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">10</span>
                                <span class="stat-label" data-fr="Modules" data-ar="وحدة">Modules</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">50+</span>
                                <span class="stat-label" data-fr="Exercices" data-ar="تمرين">Exercices</span>
                            </div>
                        </div>

                        <button class="cta-button" onclick="startCourse()">
                            <i class="fas fa-play"></i>
                            <span data-fr="Commencer le cours" data-ar="ابدأ الدورة">Commencer le cours</span>
                        </button>
                    </div>
                </section>

                <!-- Module Content (will be loaded dynamically) -->
                <section id="module-content" class="section">
                    <!-- Dynamic content will be loaded here -->
                </section>
            </div>
        </div>
    </main>

    <!-- Code Editor Modal -->
    <div class="modal" id="codeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-fr="Éditeur de Code" data-ar="محرر الكود">Éditeur de Code</h3>
                <button class="modal-close" onclick="closeCodeEditor()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="code-editor-container">
                    <div class="editor-toolbar">
                        <select id="languageSelect">
                            <option value="python">Python</option>
                            <option value="javascript">JavaScript</option>
                        </select>
                        <button class="btn-run" onclick="runCode()">
                            <i class="fas fa-play"></i>
                            <span data-fr="Exécuter" data-ar="تشغيل">Exécuter</span>
                        </button>
                        <button class="btn-reset" onclick="resetCode()">
                            <i class="fas fa-undo"></i>
                            <span data-fr="Réinitialiser" data-ar="إعادة تعيين">Réinitialiser</span>
                        </button>
                    </div>
                    <textarea id="codeEditor" placeholder="# Écrivez votre code ici..."></textarea>
                    <div class="output-container">
                        <h4 data-fr="Résultat :" data-ar="النتيجة:">Résultat :</h4>
                        <pre id="codeOutput"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
    <script src="js/modules.js"></script>
    <script src="js/exercises.js"></script>
    <script src="js/code-editor.js"></script>
</body>
</html>
