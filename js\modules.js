// Gestionnaire de modules de cours
class ModuleManager {
    constructor() {
        this.modules = {
            // Modules d'algorithmique
            'algo-intro': {
                title: { fr: 'Introduction aux algorithmes', ar: 'مقدمة في الخوارزميات' },
                duration: '2h',
                description: { 
                    fr: 'Découvrez les concepts fondamentaux des algorithmes et leur importance en informatique.',
                    ar: 'اكتشف المفاهيم الأساسية للخوارزميات وأهميتها في علوم الحاسوب.'
                },
                content: this.getAlgoIntroContent()
            },
            'algo-structures': {
                title: { fr: 'Structures de données', ar: 'هياكل البيانات' },
                duration: '2h',
                description: { 
                    fr: 'Apprenez les structures de données essentielles : tableaux, listes, piles et files.',
                    ar: 'تعلم هياكل البيانات الأساسية: المصفوفات والقوائم والمكدسات والطوابير.'
                },
                content: this.getAlgoStructuresContent()
            },
            'algo-sort': {
                title: { fr: 'Tri et recherche', ar: 'الترتيب والبحث' },
                duration: '2h',
                description: { 
                    fr: 'Maîtrisez les algorithmes de tri et de recherche les plus utilisés.',
                    ar: 'أتقن خوارزميات الترتيب والبحث الأكثر استخداماً.'
                },
                content: this.getAlgoSortContent()
            },
            'algo-recursion': {
                title: { fr: 'Récursivité', ar: 'العودية' },
                duration: '2h',
                description: { 
                    fr: 'Comprenez la récursivité et analysez la complexité des algorithmes.',
                    ar: 'افهم العودية وحلل تعقيد الخوارزميات.'
                },
                content: this.getAlgoRecursionContent()
            },
            'algo-graphs': {
                title: { fr: 'Graphes et arbres', ar: 'الرسوم والأشجار' },
                duration: '2h',
                description: { 
                    fr: 'Explorez les structures de graphes et d\'arbres avec leurs algorithmes.',
                    ar: 'استكشف هياكل الرسوم البيانية والأشجار مع خوارزمياتها.'
                },
                content: this.getAlgoGraphsContent()
            },
            
            // Modules Python
            'python-basics': {
                title: { fr: 'Bases de Python', ar: 'أساسيات بايثون' },
                duration: '2h',
                description: { 
                    fr: 'Apprenez la syntaxe de base de Python et les concepts fondamentaux.',
                    ar: 'تعلم الصيغة الأساسية لبايثون والمفاهيم الأساسية.'
                },
                content: this.getPythonBasicsContent()
            },
            'python-structures': {
                title: { fr: 'Structures Python', ar: 'هياكل بايثون' },
                duration: '2h',
                description: { 
                    fr: 'Découvrez les structures de données intégrées de Python.',
                    ar: 'اكتشف هياكل البيانات المدمجة في بايثون.'
                },
                content: this.getPythonStructuresContent()
            },
            'python-oop': {
                title: { fr: 'POO', ar: 'البرمجة الكائنية' },
                duration: '2h',
                description: { 
                    fr: 'Maîtrisez la programmation orientée objet en Python.',
                    ar: 'أتقن البرمجة الكائنية التوجه في بايثون.'
                },
                content: this.getPythonOOPContent()
            },
            'python-modules': {
                title: { fr: 'Modules', ar: 'الوحدات' },
                duration: '2h',
                description: { 
                    fr: 'Apprenez à utiliser et créer des modules Python.',
                    ar: 'تعلم كيفية استخدام وإنشاء وحدات بايثون.'
                },
                content: this.getPythonModulesContent()
            },
            'python-projects': {
                title: { fr: 'Projets', ar: 'المشاريع' },
                duration: '2h',
                description: { 
                    fr: 'Mettez en pratique vos connaissances avec des projets concrets.',
                    ar: 'طبق معرفتك من خلال مشاريع عملية.'
                },
                content: this.getPythonProjectsContent()
            }
        };
    }

    loadModule(moduleId) {
        const module = this.modules[moduleId];
        if (!module) {
            console.error('Module not found:', moduleId);
            return;
        }

        const contentArea = document.getElementById('module-content');
        const currentLang = window.app ? window.app.currentLanguage : 'fr';
        
        contentArea.innerHTML = `
            <div class="module-header">
                <div class="module-breadcrumb">
                    <a href="#" onclick="window.app.navigateToSection('home')" data-fr="Accueil" data-ar="الرئيسية">Accueil</a>
                    <i class="fas fa-chevron-right"></i>
                    <span>${module.title[currentLang]}</span>
                </div>
                <h1>${module.title[currentLang]}</h1>
                <p class="module-description">${module.description[currentLang]}</p>
                <div class="module-meta">
                    <span class="module-duration">
                        <i class="fas fa-clock"></i>
                        ${module.duration}
                    </span>
                </div>
            </div>
            <div class="module-content">
                ${module.content[currentLang]}
            </div>
        `;

        // Update language for new content
        if (window.app) {
            window.app.updateLanguage();
        }

        // Initialize code editors in the module
        this.initializeCodeEditors();
    }

    initializeCodeEditors() {
        document.querySelectorAll('.code-example').forEach(codeBlock => {
            const runButton = codeBlock.querySelector('.run-code');
            if (runButton) {
                runButton.addEventListener('click', () => {
                    const code = codeBlock.querySelector('code').textContent;
                    this.runInlineCode(code, codeBlock);
                });
            }
        });
    }

    runInlineCode(code, container) {
        try {
            // Simple JavaScript evaluation for demonstration
            const result = eval(code);
            const outputDiv = container.querySelector('.code-output') || this.createOutputDiv(container);
            outputDiv.textContent = result;
        } catch (error) {
            const outputDiv = container.querySelector('.code-output') || this.createOutputDiv(container);
            outputDiv.textContent = `Erreur: ${error.message}`;
            outputDiv.style.color = 'var(--accent-color)';
        }
    }

    createOutputDiv(container) {
        const outputDiv = document.createElement('div');
        outputDiv.className = 'code-output';
        outputDiv.style.cssText = `
            margin-top: 1rem;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
            font-family: 'Courier New', monospace;
        `;
        container.appendChild(outputDiv);
        return outputDiv;
    }

    getAlgoIntroContent() {
        return {
            fr: `
                <div class="lesson-section">
                    <h2>Qu'est-ce qu'un algorithme ?</h2>
                    <p>Un algorithme est une séquence d'instructions précises et non ambiguës permettant de résoudre un problème ou d'effectuer une tâche.</p>
                    
                    <h3>Caractéristiques d'un bon algorithme :</h3>
                    <ul>
                        <li><strong>Précision :</strong> Chaque étape doit être clairement définie</li>
                        <li><strong>Finitude :</strong> L'algorithme doit se terminer en un nombre fini d'étapes</li>
                        <li><strong>Efficacité :</strong> Utilisation optimale des ressources</li>
                        <li><strong>Généralité :</strong> Applicable à une classe de problèmes</li>
                    </ul>

                    <div class="example-box">
                        <h4>Exemple : Algorithme pour faire du thé</h4>
                        <ol>
                            <li>Remplir la bouilloire d'eau</li>
                            <li>Allumer le feu sous la bouilloire</li>
                            <li>Attendre que l'eau bouille</li>
                            <li>Mettre le thé dans la théière</li>
                            <li>Verser l'eau bouillante sur le thé</li>
                            <li>Laisser infuser 3-5 minutes</li>
                            <li>Servir le thé</li>
                        </ol>
                    </div>

                    <h3>Représentation des algorithmes</h3>
                    <p>Les algorithmes peuvent être représentés de plusieurs façons :</p>
                    <ul>
                        <li><strong>Langage naturel :</strong> Description en français</li>
                        <li><strong>Pseudo-code :</strong> Notation structurée proche du code</li>
                        <li><strong>Organigramme :</strong> Représentation graphique</li>
                        <li><strong>Code :</strong> Implémentation dans un langage de programmation</li>
                    </ul>

                    <div class="exercise-box">
                        <h4>Exercice pratique</h4>
                        <p>Écrivez un algorithme pour trouver le plus grand nombre dans une liste de nombres.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('algo-intro-ex1')">
                            Commencer l'exercice
                        </button>
                    </div>
                </div>
            `,
            ar: `
                <div class="lesson-section">
                    <h2>ما هي الخوارزمية؟</h2>
                    <p>الخوارزمية هي تسلسل من التعليمات الدقيقة وغير الغامضة التي تسمح بحل مشكلة أو تنفيذ مهمة.</p>
                    
                    <h3>خصائص الخوارزمية الجيدة:</h3>
                    <ul>
                        <li><strong>الدقة:</strong> يجب تعريف كل خطوة بوضوح</li>
                        <li><strong>النهائية:</strong> يجب أن تنتهي الخوارزمية في عدد محدود من الخطوات</li>
                        <li><strong>الكفاءة:</strong> الاستخدام الأمثل للموارد</li>
                        <li><strong>العمومية:</strong> قابلة للتطبيق على فئة من المشاكل</li>
                    </ul>

                    <div class="example-box">
                        <h4>مثال: خوارزمية لتحضير الشاي</h4>
                        <ol>
                            <li>ملء الغلاية بالماء</li>
                            <li>إشعال النار تحت الغلاية</li>
                            <li>انتظار غليان الماء</li>
                            <li>وضع الشاي في إبريق الشاي</li>
                            <li>سكب الماء المغلي على الشاي</li>
                            <li>ترك الشاي ينقع لمدة 3-5 دقائق</li>
                            <li>تقديم الشاي</li>
                        </ol>
                    </div>

                    <h3>تمثيل الخوارزميات</h3>
                    <p>يمكن تمثيل الخوارزميات بعدة طرق:</p>
                    <ul>
                        <li><strong>اللغة الطبيعية:</strong> وصف بالعربية</li>
                        <li><strong>الكود الوهمي:</strong> تدوين منظم قريب من الكود</li>
                        <li><strong>المخطط الانسيابي:</strong> تمثيل بياني</li>
                        <li><strong>الكود:</strong> تنفيذ بلغة برمجة</li>
                    </ul>

                    <div class="exercise-box">
                        <h4>تمرين عملي</h4>
                        <p>اكتب خوارزمية لإيجاد أكبر رقم في قائمة من الأرقام.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('algo-intro-ex1')">
                            ابدأ التمرين
                        </button>
                    </div>
                </div>
            `
        };
    }

    getAlgoStructuresContent() {
        return {
            fr: `
                <div class="lesson-section">
                    <h2>Les structures de données fondamentales</h2>
                    <p>Les structures de données sont des façons d'organiser et de stocker les données pour permettre un accès et une modification efficaces.</p>
                    
                    <h3>1. Les tableaux (Arrays)</h3>
                    <p>Un tableau est une collection d'éléments de même type, stockés dans des emplacements mémoire contigus.</p>
                    
                    <div class="code-example">
                        <h4>Exemple de tableau :</h4>
                        <pre><code>tableau = [1, 2, 3, 4, 5]
// Accès à un élément : tableau[0] = 1
// Taille du tableau : 5 éléments</code></pre>
                        <button class="run-code">Exécuter</button>
                    </div>

                    <h3>2. Les listes chaînées</h3>
                    <p>Une liste chaînée est une structure où chaque élément (nœud) contient des données et une référence vers l'élément suivant.</p>

                    <h3>3. Les piles (Stack)</h3>
                    <p>Structure LIFO (Last In, First Out) - le dernier élément ajouté est le premier à être retiré.</p>
                    
                    <div class="operations-box">
                        <h4>Opérations principales :</h4>
                        <ul>
                            <li><strong>Push :</strong> Ajouter un élément au sommet</li>
                            <li><strong>Pop :</strong> Retirer l'élément du sommet</li>
                            <li><strong>Top/Peek :</strong> Consulter l'élément du sommet</li>
                        </ul>
                    </div>

                    <h3>4. Les files (Queue)</h3>
                    <p>Structure FIFO (First In, First Out) - le premier élément ajouté est le premier à être retiré.</p>
                </div>
            `,
            ar: `
                <div class="lesson-section">
                    <h2>هياكل البيانات الأساسية</h2>
                    <p>هياكل البيانات هي طرق لتنظيم وتخزين البيانات للسماح بالوصول والتعديل بكفاءة.</p>
                    
                    <h3>1. المصفوفات (Arrays)</h3>
                    <p>المصفوفة هي مجموعة من العناصر من نفس النوع، مخزنة في مواقع ذاكرة متجاورة.</p>
                    
                    <div class="code-example">
                        <h4>مثال على مصفوفة:</h4>
                        <pre><code>مصفوفة = [1, 2, 3, 4, 5]
// الوصول لعنصر: مصفوفة[0] = 1
// حجم المصفوفة: 5 عناصر</code></pre>
                        <button class="run-code">تشغيل</button>
                    </div>

                    <h3>2. القوائم المترابطة</h3>
                    <p>القائمة المترابطة هي هيكل حيث كل عنصر (عقدة) يحتوي على بيانات ومرجع للعنصر التالي.</p>

                    <h3>3. المكدسات (Stack)</h3>
                    <p>هيكل LIFO (آخر داخل، أول خارج) - آخر عنصر مضاف هو أول عنصر يتم إزالته.</p>
                    
                    <div class="operations-box">
                        <h4>العمليات الرئيسية:</h4>
                        <ul>
                            <li><strong>Push:</strong> إضافة عنصر للقمة</li>
                            <li><strong>Pop:</strong> إزالة عنصر من القمة</li>
                            <li><strong>Top/Peek:</strong> عرض عنصر القمة</li>
                        </ul>
                    </div>

                    <h3>4. الطوابير (Queue)</h3>
                    <p>هيكل FIFO (أول داخل، أول خارج) - أول عنصر مضاف هو أول عنصر يتم إزالته.</p>
                </div>
            `
        };
    }

    getAlgoSortContent() {
        return {
            fr: `
                <div class="lesson-section">
                    <h2>Algorithmes de tri</h2>
                    <p>Le tri est l'une des opérations les plus fondamentales en informatique. Il consiste à organiser les éléments d'une collection selon un ordre défini.</p>

                    <h3>1. Tri par sélection</h3>
                    <p>Principe : Trouver le plus petit élément et le placer en première position, puis répéter pour le reste de la liste.</p>

                    <div class="code-example">
                        <h4>Algorithme du tri par sélection :</h4>
                        <pre><code>fonction triSelection(tableau):
    pour i de 0 à longueur(tableau) - 2:
        indexMin = i
        pour j de i + 1 à longueur(tableau) - 1:
            si tableau[j] < tableau[indexMin]:
                indexMin = j
        échanger(tableau[i], tableau[indexMin])
    retourner tableau</code></pre>
                        <button class="run-code">Visualiser</button>
                    </div>

                    <h3>2. Tri à bulles</h3>
                    <p>Principe : Comparer les éléments adjacents et les échanger s'ils sont dans le mauvais ordre.</p>

                    <div class="code-example">
                        <h4>Algorithme du tri à bulles :</h4>
                        <pre><code>fonction triBulles(tableau):
    pour i de 0 à longueur(tableau) - 2:
        pour j de 0 à longueur(tableau) - 2 - i:
            si tableau[j] > tableau[j + 1]:
                échanger(tableau[j], tableau[j + 1])
    retourner tableau</code></pre>
                        <button class="run-code">Visualiser</button>
                    </div>

                    <h3>3. Tri rapide (QuickSort)</h3>
                    <p>Principe : Diviser la liste autour d'un pivot et trier récursivement les sous-listes.</p>

                    <h2>Algorithmes de recherche</h2>

                    <h3>1. Recherche linéaire</h3>
                    <p>Parcourir séquentiellement tous les éléments jusqu'à trouver l'élément recherché.</p>

                    <div class="code-example">
                        <h4>Recherche linéaire :</h4>
                        <pre><code>fonction rechercheLineaire(tableau, element):
    pour i de 0 à longueur(tableau) - 1:
        si tableau[i] == element:
            retourner i
    retourner -1  // Élément non trouvé</code></pre>
                        <button class="run-code">Tester</button>
                    </div>

                    <h3>2. Recherche binaire</h3>
                    <p>Recherche efficace dans un tableau trié en divisant l'espace de recherche par deux à chaque étape.</p>

                    <div class="code-example">
                        <h4>Recherche binaire :</h4>
                        <pre><code>fonction rechercheBinaire(tableau, element):
    debut = 0
    fin = longueur(tableau) - 1

    tant que debut <= fin:
        milieu = (debut + fin) / 2
        si tableau[milieu] == element:
            retourner milieu
        sinon si tableau[milieu] < element:
            debut = milieu + 1
        sinon:
            fin = milieu - 1

    retourner -1  // Élément non trouvé</code></pre>
                        <button class="run-code">Tester</button>
                    </div>

                    <div class="exercise-box">
                        <h4>Exercice pratique</h4>
                        <p>Implémentez un algorithme de tri par insertion et testez-le avec différents tableaux.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('algo-sort-ex1')">
                            Commencer l'exercice
                        </button>
                    </div>
                </div>
            `,
            ar: `
                <div class="lesson-section">
                    <h2>خوارزميات الترتيب</h2>
                    <p>الترتيب هو إحدى العمليات الأساسية في علوم الحاسوب. يتمثل في تنظيم عناصر مجموعة وفقاً لترتيب محدد.</p>

                    <h3>1. الترتيب بالاختيار</h3>
                    <p>المبدأ: العثور على أصغر عنصر ووضعه في الموضع الأول، ثم تكرار العملية لبقية القائمة.</p>

                    <div class="code-example">
                        <h4>خوارزمية الترتيب بالاختيار:</h4>
                        <pre><code>دالة ترتيب_بالاختيار(مصفوفة):
    لكل i من 0 إلى طول(مصفوفة) - 2:
        فهرس_الأصغر = i
        لكل j من i + 1 إلى طول(مصفوفة) - 1:
            إذا كان مصفوفة[j] < مصفوفة[فهرس_الأصغر]:
                فهرس_الأصغر = j
        تبديل(مصفوفة[i], مصفوفة[فهرس_الأصغر])
    إرجاع مصفوفة</code></pre>
                        <button class="run-code">عرض مرئي</button>
                    </div>

                    <h3>2. الترتيب الفقاعي</h3>
                    <p>المبدأ: مقارنة العناصر المتجاورة وتبديلها إذا كانت في الترتيب الخاطئ.</p>

                    <div class="code-example">
                        <h4>خوارزمية الترتيب الفقاعي:</h4>
                        <pre><code>دالة ترتيب_فقاعي(مصفوفة):
    لكل i من 0 إلى طول(مصفوفة) - 2:
        لكل j من 0 إلى طول(مصفوفة) - 2 - i:
            إذا كان مصفوفة[j] > مصفوفة[j + 1]:
                تبديل(مصفوفة[j], مصفوفة[j + 1])
    إرجاع مصفوفة</code></pre>
                        <button class="run-code">عرض مرئي</button>
                    </div>

                    <h3>3. الترتيب السريع</h3>
                    <p>المبدأ: تقسيم القائمة حول محور وترتيب القوائم الفرعية بشكل تكراري.</p>

                    <h2>خوارزميات البحث</h2>

                    <h3>1. البحث الخطي</h3>
                    <p>تصفح جميع العناصر بالتسلسل حتى العثور على العنصر المطلوب.</p>

                    <div class="code-example">
                        <h4>البحث الخطي:</h4>
                        <pre><code>دالة بحث_خطي(مصفوفة, عنصر):
    لكل i من 0 إلى طول(مصفوفة) - 1:
        إذا كان مصفوفة[i] == عنصر:
            إرجاع i
    إرجاع -1  // العنصر غير موجود</code></pre>
                        <button class="run-code">اختبار</button>
                    </div>

                    <h3>2. البحث الثنائي</h3>
                    <p>بحث فعال في مصفوفة مرتبة بتقسيم مساحة البحث إلى النصف في كل خطوة.</p>

                    <div class="code-example">
                        <h4>البحث الثنائي:</h4>
                        <pre><code>دالة بحث_ثنائي(مصفوفة, عنصر):
    البداية = 0
    النهاية = طول(مصفوفة) - 1

    بينما البداية <= النهاية:
        الوسط = (البداية + النهاية) / 2
        إذا كان مصفوفة[الوسط] == عنصر:
            إرجاع الوسط
        وإلا إذا كان مصفوفة[الوسط] < عنصر:
            البداية = الوسط + 1
        وإلا:
            النهاية = الوسط - 1

    إرجاع -1  // العنصر غير موجود</code></pre>
                        <button class="run-code">اختبار</button>
                    </div>

                    <div class="exercise-box">
                        <h4>تمرين عملي</h4>
                        <p>نفذ خوارزمية الترتيب بالإدراج واختبرها مع مصفوفات مختلفة.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('algo-sort-ex1')">
                            ابدأ التمرين
                        </button>
                    </div>
                </div>
            `
        };
    }

    getAlgoRecursionContent() {
        return {
            fr: `
                <div class="lesson-section">
                    <h2>La récursivité</h2>
                    <p>La récursivité est une technique de programmation où une fonction s'appelle elle-même pour résoudre un problème plus petit.</p>

                    <h3>Principe de base</h3>
                    <p>Une fonction récursive doit avoir :</p>
                    <ul>
                        <li><strong>Cas de base :</strong> Condition d'arrêt qui termine la récursion</li>
                        <li><strong>Cas récursif :</strong> La fonction s'appelle avec un problème plus petit</li>
                    </ul>

                    <div class="example-box">
                        <h4>Exemple classique : Factorielle</h4>
                        <p>n! = n × (n-1) × (n-2) × ... × 1</p>
                        <div class="code-example">
                            <pre><code>fonction factorielle(n):
    // Cas de base
    si n <= 1:
        retourner 1
    // Cas récursif
    sinon:
        retourner n * factorielle(n - 1)</code></pre>
                            <button class="run-code">Tester</button>
                        </div>
                    </div>

                    <h3>Suite de Fibonacci</h3>
                    <p>Chaque nombre est la somme des deux précédents : 0, 1, 1, 2, 3, 5, 8, 13...</p>

                    <div class="code-example">
                        <h4>Fibonacci récursif :</h4>
                        <pre><code>fonction fibonacci(n):
    // Cas de base
    si n <= 1:
        retourner n
    // Cas récursif
    sinon:
        retourner fibonacci(n - 1) + fibonacci(n - 2)</code></pre>
                        <button class="run-code">Tester</button>
                    </div>

                    <h3>Complexité algorithmique</h3>
                    <p>L'analyse de la complexité permet d'évaluer l'efficacité d'un algorithme.</p>

                    <h4>Notation Big O</h4>
                    <ul>
                        <li><strong>O(1) :</strong> Temps constant</li>
                        <li><strong>O(log n) :</strong> Logarithmique</li>
                        <li><strong>O(n) :</strong> Linéaire</li>
                        <li><strong>O(n²) :</strong> Quadratique</li>
                        <li><strong>O(2ⁿ) :</strong> Exponentielle</li>
                    </ul>

                    <div class="exercise-box">
                        <h4>Exercice pratique</h4>
                        <p>Implémentez une fonction récursive pour calculer la puissance d'un nombre (x^n).</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('algo-recursion-ex1')">
                            Commencer l'exercice
                        </button>
                    </div>
                </div>
            `,
            ar: `
                <div class="lesson-section">
                    <h2>العودية (Recursion)</h2>
                    <p>العودية هي تقنية برمجة حيث تستدعي الدالة نفسها لحل مشكلة أصغر.</p>

                    <h3>المبدأ الأساسي</h3>
                    <p>يجب أن تحتوي الدالة العودية على:</p>
                    <ul>
                        <li><strong>الحالة الأساسية:</strong> شرط التوقف الذي ينهي العودية</li>
                        <li><strong>الحالة العودية:</strong> الدالة تستدعي نفسها مع مشكلة أصغر</li>
                    </ul>

                    <div class="example-box">
                        <h4>مثال كلاسيكي: المضروب</h4>
                        <p>!n = n × (n-1) × (n-2) × ... × 1</p>
                        <div class="code-example">
                            <pre><code>دالة مضروب(n):
    // الحالة الأساسية
    إذا كان n <= 1:
        إرجاع 1
    // الحالة العودية
    وإلا:
        إرجاع n * مضروب(n - 1)</code></pre>
                            <button class="run-code">اختبار</button>
                        </div>
                    </div>

                    <h3>متتالية فيبوناتشي</h3>
                    <p>كل رقم هو مجموع الرقمين السابقين: 0, 1, 1, 2, 3, 5, 8, 13...</p>

                    <div class="code-example">
                        <h4>فيبوناتشي العودي:</h4>
                        <pre><code>دالة فيبوناتشي(n):
    // الحالة الأساسية
    إذا كان n <= 1:
        إرجاع n
    // الحالة العودية
    وإلا:
        إرجاع فيبوناتشي(n - 1) + فيبوناتشي(n - 2)</code></pre>
                        <button class="run-code">اختبار</button>
                    </div>

                    <h3>تعقيد الخوارزميات</h3>
                    <p>تحليل التعقيد يسمح بتقييم كفاءة الخوارزمية.</p>

                    <h4>رمز Big O</h4>
                    <ul>
                        <li><strong>O(1):</strong> وقت ثابت</li>
                        <li><strong>O(log n):</strong> لوغاريتمي</li>
                        <li><strong>O(n):</strong> خطي</li>
                        <li><strong>O(n²):</strong> تربيعي</li>
                        <li><strong>O(2ⁿ):</strong> أسي</li>
                    </ul>

                    <div class="exercise-box">
                        <h4>تمرين عملي</h4>
                        <p>نفذ دالة عودية لحساب قوة رقم (x^n).</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('algo-recursion-ex1')">
                            ابدأ التمرين
                        </button>
                    </div>
                </div>
            `
        };
    }

    getAlgoGraphsContent() {
        return {
            fr: `
                <div class="lesson-section">
                    <h2>Graphes et arbres</h2>
                    <p>Les graphes et arbres sont des structures de données fondamentales pour représenter des relations entre objets.</p>

                    <h3>Les graphes</h3>
                    <p>Un graphe est un ensemble de sommets (nœuds) reliés par des arêtes.</p>

                    <h4>Types de graphes :</h4>
                    <ul>
                        <li><strong>Graphe orienté :</strong> Les arêtes ont une direction</li>
                        <li><strong>Graphe non orienté :</strong> Les arêtes sont bidirectionnelles</li>
                        <li><strong>Graphe pondéré :</strong> Les arêtes ont un poids</li>
                        <li><strong>Graphe connexe :</strong> Tous les sommets sont reliés</li>
                    </ul>

                    <h3>Représentation des graphes</h3>

                    <h4>1. Matrice d'adjacence</h4>
                    <div class="code-example">
                        <pre><code>// Graphe avec 4 sommets (0, 1, 2, 3)
matrice = [
    [0, 1, 1, 0],  // Sommet 0 connecté à 1 et 2
    [1, 0, 0, 1],  // Sommet 1 connecté à 0 et 3
    [1, 0, 0, 1],  // Sommet 2 connecté à 0 et 3
    [0, 1, 1, 0]   // Sommet 3 connecté à 1 et 2
]</code></pre>
                    </div>

                    <h4>2. Liste d'adjacence</h4>
                    <div class="code-example">
                        <pre><code>// Même graphe en liste d'adjacence
graphe = {
    0: [1, 2],
    1: [0, 3],
    2: [0, 3],
    3: [1, 2]
}</code></pre>
                    </div>

                    <h3>Les arbres</h3>
                    <p>Un arbre est un graphe connexe sans cycle, avec un sommet racine.</p>

                    <h4>Propriétés des arbres :</h4>
                    <ul>
                        <li>Un seul chemin entre deux sommets</li>
                        <li>n sommets ⟹ n-1 arêtes</li>
                        <li>Structure hiérarchique</li>
                    </ul>

                    <h3>Parcours de graphes</h3>

                    <h4>1. Parcours en largeur (BFS)</h4>
                    <div class="code-example">
                        <pre><code>fonction BFS(graphe, debut):
    file = [debut]
    visite = {debut}

    tant que file n'est pas vide:
        sommet = file.defiler()
        traiter(sommet)

        pour chaque voisin de sommet:
            si voisin non visite:
                visite.ajouter(voisin)
                file.enfiler(voisin)</code></pre>
                        <button class="run-code">Visualiser</button>
                    </div>

                    <h4>2. Parcours en profondeur (DFS)</h4>
                    <div class="code-example">
                        <pre><code>fonction DFS(graphe, sommet, visite):
    visite.ajouter(sommet)
    traiter(sommet)

    pour chaque voisin de sommet:
        si voisin non visite:
            DFS(graphe, voisin, visite)</code></pre>
                        <button class="run-code">Visualiser</button>
                    </div>

                    <div class="exercise-box">
                        <h4>Exercice pratique</h4>
                        <p>Implémentez un algorithme pour détecter s'il existe un chemin entre deux sommets dans un graphe.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('algo-graphs-ex1')">
                            Commencer l'exercice
                        </button>
                    </div>
                </div>
            `,
            ar: `
                <div class="lesson-section">
                    <h2>الرسوم البيانية والأشجار</h2>
                    <p>الرسوم البيانية والأشجار هي هياكل بيانات أساسية لتمثيل العلاقات بين الكائنات.</p>

                    <h3>الرسوم البيانية</h3>
                    <p>الرسم البياني هو مجموعة من القمم (العقد) مترابطة بالحواف.</p>

                    <h4>أنواع الرسوم البيانية:</h4>
                    <ul>
                        <li><strong>رسم موجه:</strong> الحواف لها اتجاه</li>
                        <li><strong>رسم غير موجه:</strong> الحواف ثنائية الاتجاه</li>
                        <li><strong>رسم مرجح:</strong> الحواف لها وزن</li>
                        <li><strong>رسم متصل:</strong> جميع القمم مترابطة</li>
                    </ul>

                    <h3>تمثيل الرسوم البيانية</h3>

                    <h4>1. مصفوفة التجاور</h4>
                    <div class="code-example">
                        <pre><code>// رسم بياني مع 4 قمم (0, 1, 2, 3)
مصفوفة = [
    [0, 1, 1, 0],  // القمة 0 متصلة بـ 1 و 2
    [1, 0, 0, 1],  // القمة 1 متصلة بـ 0 و 3
    [1, 0, 0, 1],  // القمة 2 متصلة بـ 0 و 3
    [0, 1, 1, 0]   // القمة 3 متصلة بـ 1 و 2
]</code></pre>
                    </div>

                    <h4>2. قائمة التجاور</h4>
                    <div class="code-example">
                        <pre><code>// نفس الرسم البياني بقائمة التجاور
رسم_بياني = {
    0: [1, 2],
    1: [0, 3],
    2: [0, 3],
    3: [1, 2]
}</code></pre>
                    </div>

                    <h3>الأشجار</h3>
                    <p>الشجرة هي رسم بياني متصل بدون دورات، مع قمة جذر.</p>

                    <h4>خصائص الأشجار:</h4>
                    <ul>
                        <li>مسار واحد فقط بين أي قمتين</li>
                        <li>n قمة ⟹ n-1 حافة</li>
                        <li>هيكل هرمي</li>
                    </ul>

                    <h3>تصفح الرسوم البيانية</h3>

                    <h4>1. التصفح بالعرض (BFS)</h4>
                    <div class="code-example">
                        <pre><code>دالة BFS(رسم_بياني, البداية):
    طابور = [البداية]
    زيارة = {البداية}

    بينما الطابور ليس فارغاً:
        قمة = طابور.إزالة_من_المقدمة()
        معالجة(قمة)

        لكل جار من قمة:
            إذا لم تتم زيارة الجار:
                زيارة.إضافة(جار)
                طابور.إضافة_للنهاية(جار)</code></pre>
                        <button class="run-code">عرض مرئي</button>
                    </div>

                    <h4>2. التصفح بالعمق (DFS)</h4>
                    <div class="code-example">
                        <pre><code>دالة DFS(رسم_بياني, قمة, زيارة):
    زيارة.إضافة(قمة)
    معالجة(قمة)

    لكل جار من قمة:
        إذا لم تتم زيارة الجار:
            DFS(رسم_بياني, جار, زيارة)</code></pre>
                        <button class="run-code">عرض مرئي</button>
                    </div>

                    <div class="exercise-box">
                        <h4>تمرين عملي</h4>
                        <p>نفذ خوارزمية لاكتشاف وجود مسار بين قمتين في رسم بياني.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('algo-graphs-ex1')">
                            ابدأ التمرين
                        </button>
                    </div>
                </div>
            `
        };
    }

    // Python modules content
    getPythonBasicsContent() {
        return {
            fr: `
                <div class="lesson-section">
                    <h2>Introduction à Python</h2>
                    <p>Python est un langage de programmation polyvalent, facile à apprendre et très populaire en développement web, science des données et intelligence artificielle.</p>

                    <h3>Pourquoi Python ?</h3>
                    <ul>
                        <li><strong>Syntaxe simple :</strong> Code lisible et facile à comprendre</li>
                        <li><strong>Polyvalent :</strong> Web, IA, analyse de données, automatisation</li>
                        <li><strong>Grande communauté :</strong> Nombreuses bibliothèques disponibles</li>
                        <li><strong>Multiplateforme :</strong> Fonctionne sur Windows, Mac, Linux</li>
                    </ul>

                    <h3>Variables et types de données</h3>

                    <div class="code-example">
                        <h4>Types de base :</h4>
                        <pre><code># Nombres entiers
age = 25
nombre_etudiants = 100

# Nombres décimaux
prix = 19.99
temperature = -5.5

# Chaînes de caractères
nom = "Ahmed"
message = 'Bonjour tout le monde!'

# Booléens
est_etudiant = True
a_termine = False

# Affichage
print("Nom:", nom)
print("Age:", age)
print("Prix:", prix)</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Opérateurs</h3>

                    <div class="code-example">
                        <h4>Opérateurs arithmétiques :</h4>
                        <pre><code># Addition, soustraction, multiplication, division
a = 10
b = 3

print("Addition:", a + b)        # 13
print("Soustraction:", a - b)    # 7
print("Multiplication:", a * b)  # 30
print("Division:", a / b)        # 3.333...
print("Division entière:", a // b) # 3
print("Modulo:", a % b)          # 1
print("Puissance:", a ** b)      # 1000</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Structures de contrôle</h3>

                    <h4>Conditions (if/elif/else)</h4>
                    <div class="code-example">
                        <pre><code>note = 85

if note >= 90:
    mention = "Excellent"
elif note >= 80:
    mention = "Très bien"
elif note >= 70:
    mention = "Bien"
elif note >= 60:
    mention = "Passable"
else:
    mention = "Insuffisant"

print(f"Note: {note}, Mention: {mention}")</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h4>Boucles</h4>
                    <div class="code-example">
                        <h4>Boucle for :</h4>
                        <pre><code># Boucle avec range
for i in range(5):
    print(f"Itération {i}")

# Boucle sur une liste
fruits = ["pomme", "banane", "orange"]
for fruit in fruits:
    print(f"J'aime les {fruit}s")

# Boucle avec enumerate
for index, fruit in enumerate(fruits):
    print(f"{index}: {fruit}")</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <div class="code-example">
                        <h4>Boucle while :</h4>
                        <pre><code>compteur = 0
while compteur < 5:
    print(f"Compteur: {compteur}")
    compteur += 1

# Boucle avec condition de sortie
nombre = 1
while True:
    print(nombre)
    nombre *= 2
    if nombre > 100:
        break</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Fonctions</h3>
                    <div class="code-example">
                        <pre><code>def saluer(nom, age=None):
    """Fonction pour saluer une personne"""
    if age:
        return f"Bonjour {nom}, vous avez {age} ans!"
    else:
        return f"Bonjour {nom}!"

def calculer_moyenne(*notes):
    """Calcule la moyenne de plusieurs notes"""
    if not notes:
        return 0
    return sum(notes) / len(notes)

# Utilisation
print(saluer("Ahmed"))
print(saluer("Fatma", 22))
print("Moyenne:", calculer_moyenne(15, 18, 12, 16))</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <div class="exercise-box">
                        <h4>Exercice pratique</h4>
                        <p>Créez un programme qui demande l'âge de l'utilisateur et affiche s'il est mineur, majeur ou senior (65+).</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('python-basics-ex1')">
                            Commencer l'exercice
                        </button>
                    </div>
                </div>
            `,
            ar: `
                <div class="lesson-section">
                    <h2>مقدمة في بايثون</h2>
                    <p>بايثون هي لغة برمجة متعددة الاستخدامات، سهلة التعلم ومشهورة جداً في تطوير الويب وعلوم البيانات والذكاء الاصطناعي.</p>

                    <h3>لماذا بايثون؟</h3>
                    <ul>
                        <li><strong>صيغة بسيطة:</strong> كود قابل للقراءة وسهل الفهم</li>
                        <li><strong>متعددة الاستخدامات:</strong> الويب، الذكاء الاصطناعي، تحليل البيانات، الأتمتة</li>
                        <li><strong>مجتمع كبير:</strong> مكتبات عديدة متاحة</li>
                        <li><strong>متعددة المنصات:</strong> تعمل على ويندوز وماك ولينكس</li>
                    </ul>

                    <h3>المتغيرات وأنواع البيانات</h3>

                    <div class="code-example">
                        <h4>الأنواع الأساسية:</h4>
                        <pre><code># الأرقام الصحيحة
العمر = 25
عدد_الطلاب = 100

# الأرقام العشرية
السعر = 19.99
درجة_الحرارة = -5.5

# النصوص
الاسم = "أحمد"
الرسالة = 'مرحبا بالجميع!'

# القيم المنطقية
هو_طالب = True
انتهى = False

# العرض
print("الاسم:", الاسم)
print("العمر:", العمر)
print("السعر:", السعر)</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <h3>العمليات</h3>

                    <div class="code-example">
                        <h4>العمليات الحسابية:</h4>
                        <pre><code># الجمع والطرح والضرب والقسمة
a = 10
b = 3

print("الجمع:", a + b)        # 13
print("الطرح:", a - b)        # 7
print("الضرب:", a * b)        # 30
print("القسمة:", a / b)       # 3.333...
print("القسمة الصحيحة:", a // b) # 3
print("الباقي:", a % b)       # 1
print("الأس:", a ** b)         # 1000</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <h3>هياكل التحكم</h3>

                    <h4>الشروط (if/elif/else)</h4>
                    <div class="code-example">
                        <pre><code>الدرجة = 85

if الدرجة >= 90:
    التقدير = "ممتاز"
elif الدرجة >= 80:
    التقدير = "جيد جداً"
elif الدرجة >= 70:
    التقدير = "جيد"
elif الدرجة >= 60:
    التقدير = "مقبول"
else:
    التقدير = "راسب"

print(f"الدرجة: {الدرجة}, التقدير: {التقدير}")</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <h4>الحلقات</h4>
                    <div class="code-example">
                        <h4>حلقة for:</h4>
                        <pre><code># حلقة مع range
for i in range(5):
    print(f"التكرار {i}")

# حلقة على قائمة
الفواكه = ["تفاح", "موز", "برتقال"]
for فاكهة in الفواكه:
    print(f"أحب {فاكهة}")

# حلقة مع enumerate
for الفهرس, فاكهة in enumerate(الفواكه):
    print(f"{الفهرس}: {فاكهة}")</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <h3>الدوال</h3>
                    <div class="code-example">
                        <pre><code>def تحية(الاسم, العمر=None):
    """دالة لتحية شخص"""
    if العمر:
        return f"مرحبا {الاسم}, عمرك {العمر} سنة!"
    else:
        return f"مرحبا {الاسم}!"

def حساب_المتوسط(*الدرجات):
    """حساب متوسط عدة درجات"""
    if not الدرجات:
        return 0
    return sum(الدرجات) / len(الدرجات)

# الاستخدام
print(تحية("أحمد"))
print(تحية("فاطمة", 22))
print("المتوسط:", حساب_المتوسط(15, 18, 12, 16))</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <div class="exercise-box">
                        <h4>تمرين عملي</h4>
                        <p>أنشئ برنامجاً يطلب عمر المستخدم ويعرض ما إذا كان قاصراً أم بالغاً أم كبير السن (65+).</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('python-basics-ex1')">
                            ابدأ التمرين
                        </button>
                    </div>
                </div>
            `
        };
    }
    getPythonStructuresContent() {
        return {
            fr: `
                <div class="lesson-section">
                    <h2>Structures de données en Python</h2>
                    <p>Python offre plusieurs structures de données intégrées pour organiser et manipuler les données efficacement.</p>

                    <h3>Les listes</h3>
                    <p>Les listes sont des collections ordonnées et modifiables d'éléments.</p>

                    <div class="code-example">
                        <h4>Création et manipulation de listes :</h4>
                        <pre><code># Création de listes
fruits = ["pomme", "banane", "orange"]
nombres = [1, 2, 3, 4, 5]
mixte = ["texte", 42, True, 3.14]

# Accès aux éléments
print("Premier fruit:", fruits[0])
print("Dernier fruit:", fruits[-1])

# Modification
fruits[1] = "mangue"
print("Liste modifiée:", fruits)

# Ajout d'éléments
fruits.append("kiwi")
fruits.insert(1, "fraise")
print("Après ajouts:", fruits)

# Suppression
fruits.remove("orange")
dernier = fruits.pop()
print("Après suppressions:", fruits)
print("Élément supprimé:", dernier)</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Les tuples</h3>
                    <p>Les tuples sont des collections ordonnées mais non modifiables.</p>

                    <div class="code-example">
                        <pre><code># Création de tuples
coordonnees = (10, 20)
couleurs = ("rouge", "vert", "bleu")
personne = ("Ahmed", 25, "Ingénieur")

# Accès aux éléments
print("X:", coordonnees[0])
print("Y:", coordonnees[1])

# Déballage (unpacking)
nom, age, profession = personne
print(f"{nom} a {age} ans et est {profession}")

# Les tuples sont immutables
# coordonnees[0] = 15  # Erreur!

# Mais on peut créer un nouveau tuple
nouvelles_coordonnees = (15, 25)
print("Nouvelles coordonnées:", nouvelles_coordonnees)</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Les dictionnaires</h3>
                    <p>Les dictionnaires stockent des paires clé-valeur.</p>

                    <div class="code-example">
                        <pre><code># Création de dictionnaires
etudiant = {
    "nom": "Fatma",
    "age": 22,
    "notes": [15, 18, 16],
    "actif": True
}

# Accès aux valeurs
print("Nom:", etudiant["nom"])
print("Age:", etudiant.get("age"))

# Modification et ajout
etudiant["age"] = 23
etudiant["email"] = "<EMAIL>"

# Parcours
print("\\nTous les éléments:")
for cle, valeur in etudiant.items():
    print(f"{cle}: {valeur}")

print("\\nToutes les clés:", list(etudiant.keys()))
print("Toutes les valeurs:", list(etudiant.values()))</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Les ensembles (sets)</h3>
                    <p>Les ensembles contiennent des éléments uniques non ordonnés.</p>

                    <div class="code-example">
                        <pre><code># Création d'ensembles
nombres = {1, 2, 3, 4, 5}
lettres = set("abcdef")

# Ajout et suppression
nombres.add(6)
nombres.discard(1)
print("Nombres:", nombres)

# Opérations sur les ensembles
ensemble1 = {1, 2, 3, 4}
ensemble2 = {3, 4, 5, 6}

print("Union:", ensemble1 | ensemble2)
print("Intersection:", ensemble1 & ensemble2)
print("Différence:", ensemble1 - ensemble2)
print("Différence symétrique:", ensemble1 ^ ensemble2)</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Compréhensions de listes</h3>
                    <p>Syntaxe concise pour créer des listes basées sur d'autres itérables.</p>

                    <div class="code-example">
                        <pre><code># Compréhension de liste simple
carres = [x**2 for x in range(10)]
print("Carrés:", carres)

# Avec condition
pairs = [x for x in range(20) if x % 2 == 0]
print("Nombres pairs:", pairs)

# Transformation de chaînes
mots = ["python", "java", "javascript"]
majuscules = [mot.upper() for mot in mots]
print("En majuscules:", majuscules)

# Compréhension de dictionnaire
notes = {"Ahmed": 15, "Fatma": 18, "Ali": 12}
mentions = {nom: "Admis" if note >= 10 else "Refusé"
           for nom, note in notes.items()}
print("Mentions:", mentions)

# Compréhension d'ensemble
voyelles = {char for char in "hello world" if char in "aeiou"}
print("Voyelles uniques:", voyelles)</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <div class="exercise-box">
                        <h4>Exercice pratique</h4>
                        <p>Créez un programme de gestion d'étudiants utilisant un dictionnaire pour stocker les informations et calculer les moyennes.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('python-structures-ex1')">
                            Commencer l'exercice
                        </button>
                    </div>
                </div>
            `,
            ar: `
                <div class="lesson-section">
                    <h2>هياكل البيانات في بايثون</h2>
                    <p>تقدم بايثون عدة هياكل بيانات مدمجة لتنظيم ومعالجة البيانات بكفاءة.</p>

                    <h3>القوائم (Lists)</h3>
                    <p>القوائم هي مجموعات مرتبة وقابلة للتعديل من العناصر.</p>

                    <div class="code-example">
                        <h4>إنشاء ومعالجة القوائم:</h4>
                        <pre><code># إنشاء القوائم
الفواكه = ["تفاح", "موز", "برتقال"]
الأرقام = [1, 2, 3, 4, 5]
مختلط = ["نص", 42, True, 3.14]

# الوصول للعناصر
print("أول فاكهة:", الفواكه[0])
print("آخر فاكهة:", الفواكه[-1])

# التعديل
الفواكه[1] = "مانجو"
print("القائمة المعدلة:", الفواكه)

# إضافة عناصر
الفواكه.append("كيوي")
الفواكه.insert(1, "فراولة")
print("بعد الإضافات:", الفواكه)

# الحذف
الفواكه.remove("برتقال")
الأخير = الفواكه.pop()
print("بعد الحذف:", الفواكه)
print("العنصر المحذوف:", الأخير)</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <h3>الصفوف (Tuples)</h3>
                    <p>الصفوف هي مجموعات مرتبة لكن غير قابلة للتعديل.</p>

                    <div class="code-example">
                        <pre><code># إنشاء الصفوف
الإحداثيات = (10, 20)
الألوان = ("أحمر", "أخضر", "أزرق")
الشخص = ("أحمد", 25, "مهندس")

# الوصول للعناصر
print("X:", الإحداثيات[0])
print("Y:", الإحداثيات[1])

# فك التعبئة (unpacking)
الاسم, العمر, المهنة = الشخص
print(f"{الاسم} عمره {العمر} سنة وهو {المهنة}")

# الصفوف غير قابلة للتغيير
# الإحداثيات[0] = 15  # خطأ!

# لكن يمكن إنشاء صف جديد
إحداثيات_جديدة = (15, 25)
print("الإحداثيات الجديدة:", إحداثيات_جديدة)</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <h3>القواميس (Dictionaries)</h3>
                    <p>القواميس تخزن أزواج مفتاح-قيمة.</p>

                    <div class="code-example">
                        <pre><code># إنشاء القواميس
الطالب = {
    "الاسم": "فاطمة",
    "العمر": 22,
    "الدرجات": [15, 18, 16],
    "نشط": True
}

# الوصول للقيم
print("الاسم:", الطالب["الاسم"])
print("العمر:", الطالب.get("العمر"))

# التعديل والإضافة
الطالب["العمر"] = 23
الطالب["الإيميل"] = "<EMAIL>"

# التصفح
print("\\nجميع العناصر:")
for المفتاح, القيمة in الطالب.items():
    print(f"{المفتاح}: {القيمة}")

print("\\nجميع المفاتيح:", list(الطالب.keys()))
print("جميع القيم:", list(الطالب.values()))</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <h3>المجموعات (Sets)</h3>
                    <p>المجموعات تحتوي على عناصر فريدة غير مرتبة.</p>

                    <div class="code-example">
                        <pre><code># إنشاء المجموعات
الأرقام = {1, 2, 3, 4, 5}
الحروف = set("abcdef")

# الإضافة والحذف
الأرقام.add(6)
الأرقام.discard(1)
print("الأرقام:", الأرقام)

# عمليات على المجموعات
مجموعة1 = {1, 2, 3, 4}
مجموعة2 = {3, 4, 5, 6}

print("الاتحاد:", مجموعة1 | مجموعة2)
print("التقاطع:", مجموعة1 & مجموعة2)
print("الفرق:", مجموعة1 - مجموعة2)
print("الفرق المتماثل:", مجموعة1 ^ مجموعة2)</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <h3>فهم القوائم (List Comprehensions)</h3>
                    <p>صيغة مختصرة لإنشاء قوائم بناءً على متكررات أخرى.</p>

                    <div class="code-example">
                        <pre><code># فهم قائمة بسيط
المربعات = [x**2 for x in range(10)]
print("المربعات:", المربعات)

# مع شرط
الأزواج = [x for x in range(20) if x % 2 == 0]
print("الأرقام الزوجية:", الأزواج)

# تحويل النصوص
الكلمات = ["python", "java", "javascript"]
كبيرة = [كلمة.upper() for كلمة in الكلمات]
print("بأحرف كبيرة:", كبيرة)

# فهم القاموس
الدرجات = {"أحمد": 15, "فاطمة": 18, "علي": 12}
النتائج = {اسم: "ناجح" if درجة >= 10 else "راسب"
           for اسم, درجة in الدرجات.items()}
print("النتائج:", النتائج)</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <div class="exercise-box">
                        <h4>تمرين عملي</h4>
                        <p>أنشئ برنامج إدارة طلاب باستخدام قاموس لتخزين المعلومات وحساب المتوسطات.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('python-structures-ex1')">
                            ابدأ التمرين
                        </button>
                    </div>
                </div>
            `
        };
    }
    getPythonOOPContent() {
        return {
            fr: `
                <div class="lesson-section">
                    <h2>Programmation Orientée Objet (POO)</h2>
                    <p>La POO est un paradigme de programmation qui organise le code autour d'objets et de classes.</p>

                    <h3>Classes et objets</h3>
                    <p>Une classe est un modèle pour créer des objets. Un objet est une instance d'une classe.</p>

                    <div class="code-example">
                        <h4>Définition d'une classe :</h4>
                        <pre><code>class Personne:
    def __init__(self, nom, age):
        self.nom = nom
        self.age = age
        self.actif = True

    def se_presenter(self):
        return f"Je suis {self.nom} et j'ai {self.age} ans"

    def avoir_anniversaire(self):
        self.age += 1
        print(f"Joyeux anniversaire ! {self.nom} a maintenant {self.age} ans")

# Création d'objets
personne1 = Personne("Ahmed", 25)
personne2 = Personne("Fatma", 30)

print(personne1.se_presenter())
personne1.avoir_anniversaire()</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Héritage</h3>
                    <p>L'héritage permet de créer une nouvelle classe basée sur une classe existante.</p>

                    <div class="code-example">
                        <pre><code>class Etudiant(Personne):
    def __init__(self, nom, age, universite):
        super().__init__(nom, age)  # Appel du constructeur parent
        self.universite = universite
        self.notes = []

    def ajouter_note(self, note):
        self.notes.append(note)

    def calculer_moyenne(self):
        if not self.notes:
            return 0
        return sum(self.notes) / len(self.notes)

    def se_presenter(self):  # Redéfinition de méthode
        base = super().se_presenter()
        return f"{base} et j'étudie à {self.universite}"

# Utilisation
etudiant = Etudiant("Ali", 20, "Université de Tunis")
etudiant.ajouter_note(15)
etudiant.ajouter_note(18)
etudiant.ajouter_note(16)

print(etudiant.se_presenter())
print(f"Moyenne: {etudiant.calculer_moyenne():.2f}")</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Encapsulation</h3>
                    <p>L'encapsulation protège les données en contrôlant l'accès aux attributs.</p>

                    <div class="code-example">
                        <pre><code>class CompteBancaire:
    def __init__(self, titulaire, solde_initial=0):
        self.titulaire = titulaire
        self.__solde = solde_initial  # Attribut privé

    @property
    def solde(self):
        return self.__solde

    def deposer(self, montant):
        if montant > 0:
            self.__solde += montant
            return f"Dépôt de {montant}€ effectué"
        return "Montant invalide"

    def retirer(self, montant):
        if 0 < montant <= self.__solde:
            self.__solde -= montant
            return f"Retrait de {montant}€ effectué"
        return "Solde insuffisant ou montant invalide"

# Utilisation
compte = CompteBancaire("Ahmed", 1000)
print(f"Solde initial: {compte.solde}€")
print(compte.deposer(500))
print(compte.retirer(200))
print(f"Solde final: {compte.solde}€")</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <div class="exercise-box">
                        <h4>Exercice pratique</h4>
                        <p>Créez une classe Véhicule et des sous-classes Voiture et Moto avec leurs spécificités.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('python-oop-ex1')">
                            Commencer l'exercice
                        </button>
                    </div>
                </div>
            `,
            ar: `
                <div class="lesson-section">
                    <h2>البرمجة الكائنية التوجه</h2>
                    <p>البرمجة الكائنية هي نموذج برمجة ينظم الكود حول الكائنات والفئات.</p>

                    <h3>الفئات والكائنات</h3>
                    <p>الفئة هي نموذج لإنشاء الكائنات. الكائن هو مثيل من فئة.</p>

                    <div class="code-example">
                        <h4>تعريف فئة:</h4>
                        <pre><code>class شخص:
    def __init__(self, الاسم, العمر):
        self.الاسم = الاسم
        self.العمر = العمر
        self.نشط = True

    def تقديم_النفس(self):
        return f"أنا {self.الاسم} وعمري {self.العمر} سنة"

    def عيد_ميلاد(self):
        self.العمر += 1
        print(f"عيد ميلاد سعيد! {self.الاسم} أصبح عمره {self.العمر} سنة")

# إنشاء كائنات
شخص1 = شخص("أحمد", 25)
شخص2 = شخص("فاطمة", 30)

print(شخص1.تقديم_النفس())
شخص1.عيد_ميلاد()</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <h3>الوراثة</h3>
                    <p>الوراثة تسمح بإنشاء فئة جديدة بناءً على فئة موجودة.</p>

                    <div class="code-example">
                        <pre><code>class طالب(شخص):
    def __init__(self, الاسم, العمر, الجامعة):
        super().__init__(الاسم, العمر)  # استدعاء منشئ الفئة الأب
        self.الجامعة = الجامعة
        self.الدرجات = []

    def إضافة_درجة(self, درجة):
        self.الدرجات.append(درجة)

    def حساب_المتوسط(self):
        if not self.الدرجات:
            return 0
        return sum(self.الدرجات) / len(self.الدرجات)

    def تقديم_النفس(self):  # إعادة تعريف الطريقة
        الأساس = super().تقديم_النفس()
        return f"{الأساس} وأدرس في {self.الجامعة}"

# الاستخدام
طالب = طالب("علي", 20, "جامعة تونس")
طالب.إضافة_درجة(15)
طالب.إضافة_درجة(18)
طالب.إضافة_درجة(16)

print(طالب.تقديم_النفس())
print(f"المتوسط: {طالب.حساب_المتوسط():.2f}")</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <div class="exercise-box">
                        <h4>تمرين عملي</h4>
                        <p>أنشئ فئة مركبة وفئات فرعية سيارة ودراجة نارية مع خصائصها.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('python-oop-ex1')">
                            ابدأ التمرين
                        </button>
                    </div>
                </div>
            `
        };
    }

    getPythonModulesContent() {
        return {
            fr: `
                <div class="lesson-section">
                    <h2>Modules et packages Python</h2>
                    <p>Les modules permettent d'organiser et de réutiliser le code Python efficacement.</p>

                    <h3>Import de modules</h3>
                    <div class="code-example">
                        <pre><code>import math
import random
from datetime import datetime, timedelta

# Utilisation des modules
print("Pi =", math.pi)
print("Racine de 16 =", math.sqrt(16))
print("Nombre aléatoire:", random.randint(1, 100))
print("Date actuelle:", datetime.now())

# Import avec alias
import numpy as np  # Si disponible
import pandas as pd  # Si disponible</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Modules populaires</h3>
                    <ul>
                        <li><strong>os</strong> : Interaction avec le système</li>
                        <li><strong>json</strong> : Manipulation de données JSON</li>
                        <li><strong>requests</strong> : Requêtes HTTP</li>
                        <li><strong>matplotlib</strong> : Graphiques et visualisation</li>
                        <li><strong>pandas</strong> : Analyse de données</li>
                    </ul>

                    <div class="exercise-box">
                        <h4>Exercice pratique</h4>
                        <p>Utilisez différents modules pour créer un petit programme utilitaire.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('python-modules-ex1')">
                            Commencer l'exercice
                        </button>
                    </div>
                </div>
            `,
            ar: `
                <div class="lesson-section">
                    <h2>وحدات وحزم بايثون</h2>
                    <p>الوحدات تسمح بتنظيم وإعادة استخدام كود بايثون بكفاءة.</p>

                    <h3>استيراد الوحدات</h3>
                    <div class="code-example">
                        <pre><code>import math
import random
from datetime import datetime, timedelta

# استخدام الوحدات
print("Pi =", math.pi)
print("جذر 16 =", math.sqrt(16))
print("رقم عشوائي:", random.randint(1, 100))
print("التاريخ الحالي:", datetime.now())</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <div class="exercise-box">
                        <h4>تمرين عملي</h4>
                        <p>استخدم وحدات مختلفة لإنشاء برنامج مفيد صغير.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('python-modules-ex1')">
                            ابدأ التمرين
                        </button>
                    </div>
                </div>
            `
        };
    }

    getPythonProjectsContent() {
        return {
            fr: `
                <div class="lesson-section">
                    <h2>Projets Python pratiques</h2>
                    <p>Mettez en pratique vos connaissances avec des projets concrets.</p>

                    <h3>Projet 1: Calculatrice avancée</h3>
                    <div class="code-example">
                        <pre><code>class Calculatrice:
    def __init__(self):
        self.historique = []

    def calculer(self, expression):
        try:
            resultat = eval(expression)
            self.historique.append(f"{expression} = {resultat}")
            return resultat
        except:
            return "Erreur dans l'expression"

    def afficher_historique(self):
        for calcul in self.historique:
            print(calcul)

# Utilisation
calc = Calculatrice()
print(calc.calculer("2 + 3 * 4"))
print(calc.calculer("(10 + 5) / 3"))
calc.afficher_historique()</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <h3>Projet 2: Gestionnaire de tâches</h3>
                    <div class="code-example">
                        <pre><code>class GestionnaireTaches:
    def __init__(self):
        self.taches = []

    def ajouter_tache(self, description, priorite="normale"):
        tache = {
            "id": len(self.taches) + 1,
            "description": description,
            "priorite": priorite,
            "terminee": False
        }
        self.taches.append(tache)
        return f"Tâche ajoutée: {description}"

    def marquer_terminee(self, id_tache):
        for tache in self.taches:
            if tache["id"] == id_tache:
                tache["terminee"] = True
                return f"Tâche {id_tache} marquée comme terminée"
        return "Tâche non trouvée"

    def lister_taches(self):
        for tache in self.taches:
            statut = "✓" if tache["terminee"] else "○"
            print(f"{statut} {tache['id']}: {tache['description']} ({tache['priorite']})")

# Test
gestionnaire = GestionnaireTaches()
gestionnaire.ajouter_tache("Apprendre Python", "haute")
gestionnaire.ajouter_tache("Faire les courses")
gestionnaire.marquer_terminee(1)
gestionnaire.lister_taches()</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">Tester</button>
                    </div>

                    <div class="exercise-box">
                        <h4>Projet final</h4>
                        <p>Créez votre propre application Python en combinant tous les concepts appris.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('python-projects-ex1')">
                            Commencer le projet
                        </button>
                    </div>
                </div>
            `,
            ar: `
                <div class="lesson-section">
                    <h2>مشاريع بايثون عملية</h2>
                    <p>طبق معرفتك من خلال مشاريع ملموسة.</p>

                    <h3>المشروع 1: آلة حاسبة متقدمة</h3>
                    <div class="code-example">
                        <pre><code>class آلة_حاسبة:
    def __init__(self):
        self.التاريخ = []

    def حساب(self, التعبير):
        try:
            النتيجة = eval(التعبير)
            self.التاريخ.append(f"{التعبير} = {النتيجة}")
            return النتيجة
        except:
            return "خطأ في التعبير"

    def عرض_التاريخ(self):
        for حساب in self.التاريخ:
            print(حساب)

# الاستخدام
calc = آلة_حاسبة()
print(calc.حساب("2 + 3 * 4"))
print(calc.حساب("(10 + 5) / 3"))
calc.عرض_التاريخ()</code></pre>
                        <button class="run-code" onclick="window.codeEditor.open(this.previousElementSibling.textContent, 'python')">اختبار</button>
                    </div>

                    <div class="exercise-box">
                        <h4>المشروع النهائي</h4>
                        <p>أنشئ تطبيق بايثون خاص بك بدمج جميع المفاهيم المتعلمة.</p>
                        <button class="btn-primary" onclick="window.ExerciseManager.openExercise('python-projects-ex1')">
                            ابدأ المشروع
                        </button>
                    </div>
                </div>
            `
        };
    }
}

// Initialize module manager
window.ModuleManager = new ModuleManager();
