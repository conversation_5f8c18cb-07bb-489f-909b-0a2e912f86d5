# Cours Algorithmique et Python - دورة الخوارزميات وبايثون

Une plateforme d'apprentissage interactive bilingue (Français/Arabe) pour maîtriser les algorithmes et la programmation Python en 20 heures.

## 🎯 Objectifs

- **Algorithmique (10h)** : Maîtriser les concepts fondamentaux des algorithmes
- **Python (10h)** : Apprendre la programmation Python de A à Z
- **Apprentissage interactif** : Exercices pratiques avec éditeur de code intégré
- **Support bilingue** : Interface en français et arabe tunisien

## 📚 Contenu du cours

### Modules d'Algorithmique (10h)
1. **Introduction aux algorithmes** (2h)
   - Concepts de base
   - Complexité algorithmique
   - Pseudo-code

2. **Structures de données** (2h)
   - Tableaux et listes
   - Piles et files
   - Arbres binaires

3. **Tri et recherche** (2h)
   - Tri par sélection, bulles, insertion
   - Recherche linéaire et binaire
   - Analyse de performance

4. **Récursivité** (2h)
   - Principe de la récursion
   - Factorielle et Fibonacci
   - Optimisation par mémoïsation

5. **Graphes et arbres** (2h)
   - Représentation des graphes
   - Parcours BFS et DFS
   - Applications pratiques

### Modules Python (10h)
1. **Bases de Python** (2h)
   - Variables et types
   - Structures de contrôle
   - Fonctions

2. **Structures de données** (2h)
   - Listes, tuples, dictionnaires
   - Ensembles
   - Compréhensions

3. **Programmation orientée objet** (2h)
   - Classes et objets
   - Héritage
   - Encapsulation

4. **Modules et packages** (2h)
   - Import de modules
   - Création de packages
   - Bibliothèques standard

5. **Projets pratiques** (2h)
   - Applications complètes
   - Bonnes pratiques
   - Tests et débogage

## 🚀 Fonctionnalités

- ✅ **Interface bilingue** : Français ↔ Arabe avec support RTL
- ✅ **Éditeur de code intégré** : Exécution JavaScript et Python
- ✅ **Exercices interactifs** : Plus de 50 exercices avec solutions
- ✅ **Système de progression** : Suivi des modules complétés
- ✅ **Thèmes** : Mode sombre/clair
- ✅ **Responsive** : Compatible mobile et desktop
- ✅ **Hints système** : Indices progressifs pour les exercices

## 🛠️ Technologies utilisées

- **Frontend** : HTML5, CSS3, JavaScript (Vanilla)
- **Styling** : CSS Grid, Flexbox, CSS Custom Properties
- **Icons** : Font Awesome
- **Fonts** : Inter (Latin), Amiri (Arabic)
- **Code Execution** : JavaScript native, Pyodide (Python)

## 📁 Structure du projet

```
cours_algo_python/
├── index.html              # Page principale
├── css/
│   ├── style.css          # Styles principaux
│   └── rtl.css            # Support RTL pour l'arabe
├── js/
│   ├── app.js             # Application principale
│   ├── modules.js         # Gestion des modules de cours
│   ├── exercises.js       # Système d'exercices
│   └── code-editor.js     # Éditeur de code
└── README.md              # Documentation
```

## 🚀 Installation et utilisation

1. **Cloner le projet**
   ```bash
   git clone [URL_DU_REPO]
   cd cours_algo_python
   ```

2. **Lancer un serveur local**
   ```bash
   # Avec Python
   python -m http.server 8000
   
   # Avec Node.js
   npx serve .
   
   # Avec PHP
   php -S localhost:8000
   ```

3. **Ouvrir dans le navigateur**
   ```
   http://localhost:8000
   ```

## 🎨 Personnalisation

### Ajouter un nouveau module
1. Modifier `js/modules.js` pour ajouter le contenu
2. Mettre à jour la sidebar dans `index.html`
3. Ajouter les traductions français/arabe

### Ajouter un exercice
1. Modifier `js/exercises.js`
2. Définir le titre, description, code de départ et solution
3. Ajouter les hints et tests

### Modifier les thèmes
1. Éditer les variables CSS dans `css/style.css`
2. Ajuster les couleurs dans `[data-theme="dark"]`

## 🌍 Support multilingue

L'application utilise un système d'attributs `data-fr` et `data-ar` pour le contenu statique, et des objets JavaScript pour le contenu dynamique.

### Ajouter une traduction
```javascript
// Dans modules.js
getModuleContent() {
    return {
        fr: "Contenu en français",
        ar: "المحتوى بالعربية"
    };
}
```

## 📱 Responsive Design

- **Mobile First** : Optimisé pour les petits écrans
- **Breakpoints** : 768px (tablet), 1024px (desktop)
- **Navigation** : Menu hamburger sur mobile
- **Touch Friendly** : Boutons et zones tactiles adaptés

## 🔧 Développement

### Structure CSS
- **Variables CSS** : Couleurs et espacements centralisés
- **BEM Methodology** : Nommage des classes cohérent
- **Grid Layout** : Mise en page moderne et flexible

### JavaScript
- **Classes ES6** : Code modulaire et maintenable
- **Event Delegation** : Performance optimisée
- **Local Storage** : Sauvegarde des préférences utilisateur

## 📈 Progression et analytics

Le système de progression suit :
- Modules complétés
- Exercices réussis
- Temps passé par module
- Préférences utilisateur (langue, thème)

## 🤝 Contribution

Les contributions sont les bienvenues ! Pour contribuer :

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 👥 Auteurs

- **Développement** : Assistant IA Augment
- **Contenu pédagogique** : Expertise en algorithmique et Python
- **Design** : Interface moderne et accessible

## 🙏 Remerciements

- Font Awesome pour les icônes
- Google Fonts pour les polices
- La communauté open source pour l'inspiration

---

**Note** : Cette plateforme est conçue pour l'apprentissage interactif et peut être adaptée selon les besoins spécifiques des apprenants.
