/* Variables CSS */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Dark Theme */
[data-theme="dark"] {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-light: #9ca3af;
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --border-color: #374151;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--primary-color);
}

.logo i {
    font-size: 1.5rem;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background-color: var(--bg-secondary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.lang-toggle,
.theme-toggle,
.mobile-menu-toggle {
    background: none;
    border: 1px solid var(--border-color);
    padding: 0.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.lang-toggle:hover,
.theme-toggle:hover,
.mobile-menu-toggle:hover {
    background-color: var(--bg-secondary);
    color: var(--primary-color);
}

/* Main Layout */
.main {
    display: flex;
    margin-top: 4rem;
    min-height: calc(100vh - 4rem);
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    padding: 1.5rem;
    overflow-y: auto;
    position: fixed;
    height: calc(100vh - 4rem);
    left: 0;
    top: 4rem;
    transition: var(--transition);
}

.sidebar-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.progress-overview {
    text-align: center;
}

.progress-overview h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.progress-circle {
    position: relative;
    display: inline-block;
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: none;
    stroke: var(--border-color);
    stroke-width: 4;
    stroke-dasharray: 188.5;
    stroke-dashoffset: 188.5;
    transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: 600;
    color: var(--primary-color);
}

.module-section {
    margin-bottom: 2rem;
}

.module-section h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.module-items {
    list-style: none;
}

.module-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 0.5rem;
}

.module-item:hover {
    background-color: var(--bg-tertiary);
}

.module-item.completed {
    background-color: rgba(16, 185, 129, 0.1);
}

.module-item.current {
    background-color: rgba(59, 130, 246, 0.1);
    border: 1px solid var(--primary-color);
}

.module-item i {
    color: var(--text-light);
    width: 1rem;
}

.module-item.completed i {
    color: var(--secondary-color);
}

.module-item.current i {
    color: var(--primary-color);
}

.module-item span:first-of-type {
    flex: 1;
    font-size: 0.9rem;
}

.duration {
    font-size: 0.8rem;
    color: var(--text-light);
    background: var(--bg-tertiary);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

/* Content Area */
.content-area {
    flex: 1;
    margin-left: 300px;
    padding: 2rem;
    transition: var(--transition);
}

.section {
    display: none;
}

.section.active {
    display: block;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 0;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    line-height: 1.7;
}

.stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-bottom: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.cta-button {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-md);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: calc(90vh - 100px);
}

/* Code Editor Styles */
.code-editor-container {
    display: flex;
    flex-direction: column;
    height: 500px;
}

.editor-toolbar {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.editor-toolbar select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.btn-run, .btn-reset {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.btn-run:hover, .btn-reset:hover {
    background: var(--primary-dark);
}

.btn-reset {
    background: var(--text-secondary);
}

#codeEditor {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    padding: 1rem;
    border: none;
    outline: none;
    resize: none;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.output-container {
    height: 150px;
    border-top: 1px solid var(--border-color);
    padding: 1rem;
    background: var(--bg-secondary);
}

.output-container h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

#codeOutput {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    height: 100px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
}

/* Exercise Styles */
.exercise-modal .modal-content {
    max-width: 1200px;
    height: 90vh;
}

.exercise-content .modal-body {
    padding: 0;
    height: calc(90vh - 80px);
}

.exercise-layout {
    display: flex;
    height: 100%;
}

.exercise-instructions {
    width: 400px;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
}

.exercise-editor {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.editor-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.exercise-code-editor {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    padding: 1rem;
    border: none;
    outline: none;
    resize: none;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.exercise-output {
    height: 200px;
    padding: 1rem;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    overflow-y: auto;
}

.exercise-hints {
    margin: 1.5rem 0;
}

.hints-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.hint-item {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(59, 130, 246, 0.1);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);
}

.hint-item i {
    color: var(--primary-color);
    margin-top: 0.1rem;
}

.btn-hint {
    background: none;
    border: 1px dashed var(--border-color);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-hint:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.exercise-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.btn-primary, .btn-secondary {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.test-results {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.test-result {
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.test-result.passed {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--secondary-color);
}

.test-result.failed {
    background: rgba(239, 68, 68, 0.1);
    border-color: #ef4444;
}

.test-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.test-status {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.test-result.passed .test-status {
    color: var(--secondary-color);
}

.test-result.failed .test-status {
    color: #ef4444;
}

.test-details {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.test-details > div {
    margin-bottom: 0.25rem;
}

/* Module Content Styles */
.module-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.module-breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.module-breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.module-breadcrumb i {
    font-size: 0.8rem;
}

.module-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.module-meta {
    display: flex;
    gap: 1rem;
}

.module-duration {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-light);
    font-size: 0.9rem;
}

.lesson-section {
    margin-bottom: 3rem;
}

.lesson-section h2 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.lesson-section h3 {
    color: var(--text-primary);
    margin: 1.5rem 0 1rem 0;
    font-size: 1.2rem;
}

.lesson-section h4 {
    color: var(--text-primary);
    margin: 1rem 0 0.5rem 0;
    font-size: 1.1rem;
}

.lesson-section p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

.lesson-section ul, .lesson-section ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.lesson-section li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.example-box, .operations-box, .exercise-box {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1.5rem 0;
}

.example-box {
    border-left: 4px solid var(--secondary-color);
}

.operations-box {
    border-left: 4px solid var(--accent-color);
}

.exercise-box {
    border-left: 4px solid var(--primary-color);
}

.code-example {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin: 1rem 0;
    overflow: hidden;
}

.code-example h4 {
    background: var(--bg-tertiary);
    padding: 0.75rem 1rem;
    margin: 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.code-example pre {
    margin: 0;
    padding: 1rem;
    overflow-x: auto;
}

.code-example code {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
}

.run-code {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    margin: 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 0.9rem;
    transition: var(--transition);
}

.run-code:hover {
    background: var(--primary-dark);
}

/* Responsive */
@media (max-width: 768px) {
    .nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .content-area {
        margin-left: 0;
        padding: 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .stats {
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .exercise-layout {
        flex-direction: column;
    }

    .exercise-instructions {
        width: 100%;
        max-height: 300px;
    }

    .exercise-actions {
        flex-wrap: wrap;
    }

    .modal-content {
        width: 95%;
        height: 95vh;
    }
}
