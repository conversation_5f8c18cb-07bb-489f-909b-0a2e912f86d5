/* Variables CSS - Design Créatif */
:root {
    /* Couleurs principales - Gradient moderne */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    
    /* Couleurs solides */
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --primary-light: #a3bffa;
    
    --secondary-color: #f093fb;
    --secondary-dark: #ed64a6;
    --secondary-light: #fbb6ce;
    
    --accent-color: #4facfe;
    --accent-dark: #3182ce;
    --accent-light: #90cdf4;
    
    /* Couleurs de fond - Mode clair */
    --bg-primary: #ffffff;
    --bg-secondary: #f7fafc;
    --bg-tertiary: #edf2f7;
    --bg-glass: rgba(255, 255, 255, 0.25);
    --bg-overlay: rgba(0, 0, 0, 0.5);
    
    /* Couleurs de texte */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --text-white: #ffffff;
    
    /* Couleurs de bordure */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-gradient: linear-gradient(90deg, transparent, #e2e8f0, transparent);
    
    /* Ombres créatives */
    --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
    --shadow-float: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.08);
    --shadow-intense: 0 20px 40px rgba(0, 0, 0, 0.15);
    
    /* Rayons de bordure */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 50px;
    
    /* Espacements */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Polices modernes */
    --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-mono: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
    
    /* Tailles de police */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.5rem;
    --text-5xl: 3rem;
    
    /* Transitions et animations */
    --transition-fast: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 600ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Mode sombre */
[data-theme="dark"] {
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-glass: rgba(0, 0, 0, 0.25);
    --text-primary: #ffffff;
    --text-secondary: #a0aec0;
    --text-muted: #718096;
    --border-color: #2d3748;
    --border-light: #4a5568;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    transition: all var(--transition-normal);
}

/* Particles Background */
#particles-js {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.6;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Header moderne */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
}

/* Logo avec animation */
.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-weight: 700;
    font-size: var(--text-xl);
    color: var(--text-primary);
    text-decoration: none;
}

.logo-icon {
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    border-radius: var(--radius-md);
    color: white;
    font-size: 1.2rem;
}

.pulse-ring {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-md);
    animation: pulse-ring 2s infinite;
    opacity: 0.3;
}

@keyframes pulse-ring {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.1;
    }
    100% {
        transform: scale(1);
        opacity: 0.3;
    }
}

/* Navigation moderne */
.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-full);
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--primary-gradient);
    transition: left var(--transition-normal);
    z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 0;
}

.nav-link:hover,
.nav-link.active {
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-float);
}

/* Actions du header */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.lang-toggle,
.theme-toggle,
.mobile-menu-toggle {
    position: relative;
    background: none;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-full);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    color: var(--text-secondary);
}

.lang-toggle:hover,
.theme-toggle:hover,
.mobile-menu-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: scale(1.1);
    box-shadow: var(--shadow-glow);
}

/* Dropdown langue */
.lang-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-card);
    padding: var(--spacing-sm);
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.lang-toggle:hover .lang-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lang-option {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.lang-option:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

/* Main content */
.main {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* Floating Action Button */
.fab-container {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: 999;
}

.fab {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-float);
    transition: all var(--transition-normal);
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-intense);
}

.fab-menu {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all var(--transition-normal);
}

.fab-container:hover .fab-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.fab-item {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: white;
    border: 2px solid var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.fab-item:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* Sections */
.section {
    display: none;
    padding: var(--spacing-3xl) 0;
    min-height: calc(100vh - 80px);
}

.section.active {
    display: block;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-header h2 {
    font-size: var(--text-4xl);
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
}

.section-header p {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Hero Section */
.hero {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    padding: var(--spacing-3xl) 0;
    min-height: 80vh;
}

.hero-title {
    font-size: var(--text-5xl);
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.6;
}

/* Stats Cards */
.hero-stats {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-card);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-float);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-md);
    background: var(--accent-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-number {
    font-size: var(--text-2xl);
    font-weight: 700;
    color: var(--text-primary);
    display: block;
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* Hero Actions */
.hero-actions {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.btn-primary,
.btn-secondary {
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--text-lg);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-card);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-float);
}

.btn-secondary {
    background: white;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-3px);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: var(--text-xl);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.algorithm-animation {
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-intense);
    border: 1px solid var(--border-color);
    text-align: center;
}

.sorting-bars {
    display: flex;
    align-items: end;
    gap: 8px;
    height: 200px;
    margin-bottom: var(--spacing-lg);
    justify-content: center;
}

.bar {
    width: 30px;
    background: var(--accent-gradient);
    border-radius: 4px 4px 0 0;
    transition: all var(--transition-slow);
    animation: sortingAnimation 3s infinite;
}

@keyframes sortingAnimation {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(1.2); }
}

.algorithm-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--text-lg);
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
    margin-top: var(--spacing-3xl);
}

.feature-card {
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-card);
    border: 1px solid var(--border-color);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 4px;
    background: var(--primary-gradient);
    transition: left var(--transition-normal);
}

.feature-card:hover::before {
    left: 0;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-float);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: var(--secondary-gradient);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.feature-card h3 {
    font-size: var(--text-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Games Grid */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.game-card {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-card);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.game-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-float);
}

.game-header {
    background: var(--success-gradient);
    color: white;
    padding: var(--spacing-lg);
    text-align: center;
}

.game-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.game-title {
    font-size: var(--text-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
}

.game-difficulty {
    background: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
}

.game-body {
    padding: var(--spacing-lg);
}

.game-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.game-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    font-size: var(--text-sm);
    color: var(--text-muted);
}

.game-play-btn {
    width: 100%;
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.game-play-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card);
}

/* Quiz Container */
.quiz-container {
    max-width: 800px;
    margin: 0 auto;
}

.quiz-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-card);
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-xl);
}

.quiz-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.quiz-progress {
    background: var(--bg-secondary);
    height: 8px;
    border-radius: var(--radius-full);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
}

.quiz-progress-bar {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
}

.quiz-question {
    font-size: var(--text-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
}

.quiz-options {
    display: grid;
    gap: var(--spacing-md);
}

.quiz-option {
    background: var(--bg-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-align: left;
}

.quiz-option:hover {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.quiz-option.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.quiz-option.correct {
    border-color: #10b981;
    background: #10b981;
    color: white;
}

.quiz-option.incorrect {
    border-color: #ef4444;
    background: #ef4444;
    color: white;
}

/* Progress Dashboard */
.progress-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.progress-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-card);
    border: 1px solid var(--border-color);
    text-align: center;
}

.progress-circle {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto var(--spacing-lg);
}

.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    fill: none;
    stroke: var(--bg-secondary);
    stroke-width: 8;
}

.progress-ring-circle.progress {
    stroke: url(#progressGradient);
    stroke-linecap: round;
    transition: stroke-dasharray var(--transition-slow);
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: var(--radius-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    box-shadow: var(--shadow-intense);
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
    transition: color var(--transition-fast);
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-xl);
}

/* Animations personnalisées */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .header-content {
        padding: var(--spacing-sm) 0;
    }

    .nav {
        display: none;
    }

    .hero-title {
        font-size: var(--text-3xl);
    }

    .hero-subtitle {
        font-size: var(--text-lg);
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .stat-card {
        justify-content: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .games-grid {
        grid-template-columns: 1fr;
    }

    .progress-dashboard {
        grid-template-columns: 1fr;
    }

    .fab-container {
        bottom: var(--spacing-lg);
        right: var(--spacing-lg);
    }

    .fab {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .modal-content {
        max-width: 95vw;
        margin: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .hero {
        padding: var(--spacing-xl) 0;
    }

    .hero-title {
        font-size: var(--text-2xl);
    }

    .section {
        padding: var(--spacing-xl) 0;
    }

    .section-header h2 {
        font-size: var(--text-2xl);
    }

    .feature-card,
    .game-card,
    .quiz-card,
    .progress-card {
        padding: var(--spacing-lg);
    }

    .btn-large {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--text-base);
    }
}

/* Utilitaires */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none !important; }
.visible { display: block !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

/* Effets de survol globaux */
.hover-lift {
    transition: transform var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
}

.hover-glow {
    transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: var(--shadow-glow);
}

/* Gradients SVG pour les cercles de progression */
.progress-gradient {
    background: var(--primary-gradient);
}
