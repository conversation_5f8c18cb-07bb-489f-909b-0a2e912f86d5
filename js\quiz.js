// Système de quiz interactifs
class QuizManager {
    constructor() {
        this.quizzes = this.initQuizzes();
        this.currentQuiz = null;
        this.currentQuestion = 0;
        this.score = 0;
        this.answers = [];
        this.timeLeft = 0;
        this.timer = null;
    }

    initQuizzes() {
        return {
            basics: {
                id: 'basics',
                title: { fr: 'Bases de l\'Algorithmique', tn: 'أساسيات الخوارزميات' },
                description: { 
                    fr: 'Testez vos connaissances sur les concepts fondamentaux', 
                    tn: 'اختبر معرفتك بالمفاهيم الأساسية' 
                },
                timeLimit: 300, // 5 minutes
                questions: this.getBasicsQuestions()
            },
            
            sorting: {
                id: 'sorting',
                title: { fr: 'Algorithmes de Tri', tn: 'خوارزميات الترتيب' },
                description: { 
                    fr: 'Évaluez votre compréhension des algorithmes de tri', 
                    tn: 'قيم فهمك لخوارزميات الترتيب' 
                },
                timeLimit: 420, // 7 minutes
                questions: this.getSortingQuestions()
            },
            
            complexity: {
                id: 'complexity',
                title: { fr: 'Complexité Algorithmique', tn: 'تعقيد الخوارزميات' },
                description: { 
                    fr: 'Maîtrisez-vous la notation Big O ?', 
                    tn: 'هل تتقن تدوين Big O؟' 
                },
                timeLimit: 360, // 6 minutes
                questions: this.getComplexityQuestions()
            },
            
            dataStructures: {
                id: 'dataStructures',
                title: { fr: 'Structures de Données', tn: 'هياكل البيانات' },
                description: { 
                    fr: 'Testez vos connaissances sur les structures de données', 
                    tn: 'اختبر معرفتك بهياكل البيانات' 
                },
                timeLimit: 480, // 8 minutes
                questions: this.getDataStructuresQuestions()
            },
            
            advanced: {
                id: 'advanced',
                title: { fr: 'Algorithmes Avancés', tn: 'خوارزميات متقدمة' },
                description: { 
                    fr: 'Pour les experts : graphes, programmation dynamique...', 
                    tn: 'للخبراء: الرسوم البيانية، البرمجة الديناميكية...' 
                },
                timeLimit: 600, // 10 minutes
                questions: this.getAdvancedQuestions()
            }
        };
    }

    loadQuizContent() {
        const quizContainer = document.querySelector('.quiz-container');
        if (!quizContainer) return;

        quizContainer.innerHTML = this.createQuizSelection();
    }

    createQuizSelection() {
        const currentLang = app.currentLang;
        
        return `
            <div class="quiz-selection">
                <div class="section-header">
                    <h2>${currentLang === 'fr' ? 'Choisissez votre Quiz' : 'اختر الاختبار'}</h2>
                    <p>${currentLang === 'fr' ? 'Testez vos connaissances avec nos quiz interactifs' : 'اختبر معرفتك مع اختباراتنا التفاعلية'}</p>
                </div>
                <div class="quiz-grid">
                    ${Object.values(this.quizzes).map(quiz => this.createQuizCard(quiz)).join('')}
                </div>
            </div>
        `;
    }

    createQuizCard(quiz) {
        const currentLang = app.currentLang;
        const bestScore = this.getBestScore(quiz.id);
        const attempts = this.getAttempts(quiz.id);
        
        return `
            <div class="quiz-card hover-lift" onclick="quizManager.startQuiz('${quiz.id}')">
                <div class="quiz-header">
                    <h3>${quiz.title[currentLang]}</h3>
                    <div class="quiz-meta">
                        <span class="quiz-time">⏱️ ${Math.floor(quiz.timeLimit / 60)} min</span>
                        <span class="quiz-questions">❓ ${quiz.questions.length} ${currentLang === 'fr' ? 'questions' : 'سؤال'}</span>
                    </div>
                </div>
                <div class="quiz-body">
                    <p class="quiz-description">${quiz.description[currentLang]}</p>
                    <div class="quiz-stats">
                        <div class="stat">
                            <span class="stat-label">${currentLang === 'fr' ? 'Meilleur score' : 'أفضل نتيجة'}</span>
                            <span class="stat-value">${bestScore}%</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">${currentLang === 'fr' ? 'Tentatives' : 'محاولات'}</span>
                            <span class="stat-value">${attempts}</span>
                        </div>
                    </div>
                    <button class="quiz-start-btn">
                        <i class="fas fa-play"></i>
                        ${currentLang === 'fr' ? 'Commencer' : 'ابدأ'}
                    </button>
                </div>
            </div>
        `;
    }

    startQuiz(quizId) {
        const quiz = this.quizzes[quizId];
        if (!quiz) return;

        this.currentQuiz = quiz;
        this.currentQuestion = 0;
        this.score = 0;
        this.answers = [];
        this.timeLeft = quiz.timeLimit;

        this.showQuizInterface();
        this.startTimer();
        this.showQuestion();
        
        app.trackEvent('quiz_started', { quizId });
    }

    showQuizInterface() {
        const currentLang = app.currentLang;
        const quizContainer = document.querySelector('.quiz-container');
        
        quizContainer.innerHTML = `
            <div class="quiz-interface">
                <div class="quiz-header">
                    <div class="quiz-info">
                        <h2>${this.currentQuiz.title[currentLang]}</h2>
                        <div class="quiz-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="quizProgress"></div>
                            </div>
                            <span class="progress-text">
                                <span id="currentQ">1</span>/${this.currentQuiz.questions.length}
                            </span>
                        </div>
                    </div>
                    <div class="quiz-timer">
                        <i class="fas fa-clock"></i>
                        <span id="quizTimer">${this.formatTime(this.timeLeft)}</span>
                    </div>
                </div>
                
                <div class="quiz-content" id="quizContent">
                    <!-- Question sera injectée ici -->
                </div>
                
                <div class="quiz-controls">
                    <button id="prevBtn" onclick="quizManager.previousQuestion()" class="btn-secondary" disabled>
                        <i class="fas fa-arrow-left"></i>
                        ${currentLang === 'fr' ? 'Précédent' : 'السابق'}
                    </button>
                    <button id="nextBtn" onclick="quizManager.nextQuestion()" class="btn-primary" disabled>
                        ${currentLang === 'fr' ? 'Suivant' : 'التالي'}
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <button id="finishBtn" onclick="quizManager.finishQuiz()" class="btn-success" style="display: none;">
                        <i class="fas fa-check"></i>
                        ${currentLang === 'fr' ? 'Terminer' : 'إنهاء'}
                    </button>
                </div>
            </div>
        `;
    }

    showQuestion() {
        const question = this.currentQuiz.questions[this.currentQuestion];
        const currentLang = app.currentLang;
        const quizContent = document.getElementById('quizContent');
        
        quizContent.innerHTML = `
            <div class="question-container">
                <div class="question-number">
                    ${currentLang === 'fr' ? 'Question' : 'سؤال'} ${this.currentQuestion + 1}
                </div>
                <h3 class="question-text">${question.question[currentLang]}</h3>
                
                ${question.code ? `
                    <div class="question-code">
                        <pre><code>${question.code}</code></pre>
                    </div>
                ` : ''}
                
                <div class="question-options">
                    ${question.options.map((option, index) => `
                        <label class="option-label">
                            <input type="radio" name="answer" value="${index}" onchange="quizManager.selectAnswer(${index})">
                            <span class="option-text">${option[currentLang]}</span>
                        </label>
                    `).join('')}
                </div>
                
                ${question.explanation ? `
                    <div class="question-explanation" id="explanation" style="display: none;">
                        <h4>${currentLang === 'fr' ? 'Explication' : 'شرح'}:</h4>
                        <p>${question.explanation[currentLang]}</p>
                    </div>
                ` : ''}
            </div>
        `;

        // Restaurer la réponse précédente si elle existe
        if (this.answers[this.currentQuestion] !== undefined) {
            const radio = document.querySelector(`input[value="${this.answers[this.currentQuestion]}"]`);
            if (radio) {
                radio.checked = true;
                this.updateNavigationButtons();
            }
        }

        this.updateProgress();
        this.updateNavigationButtons();
    }

    selectAnswer(answerIndex) {
        this.answers[this.currentQuestion] = answerIndex;
        this.updateNavigationButtons();
    }

    nextQuestion() {
        if (this.currentQuestion < this.currentQuiz.questions.length - 1) {
            this.currentQuestion++;
            this.showQuestion();
        }
    }

    previousQuestion() {
        if (this.currentQuestion > 0) {
            this.currentQuestion--;
            this.showQuestion();
        }
    }

    updateProgress() {
        const progress = ((this.currentQuestion + 1) / this.currentQuiz.questions.length) * 100;
        const progressFill = document.getElementById('quizProgress');
        const currentQ = document.getElementById('currentQ');
        
        if (progressFill) progressFill.style.width = `${progress}%`;
        if (currentQ) currentQ.textContent = this.currentQuestion + 1;
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const finishBtn = document.getElementById('finishBtn');
        
        // Bouton précédent
        prevBtn.disabled = this.currentQuestion === 0;
        
        // Bouton suivant/terminer
        const hasAnswer = this.answers[this.currentQuestion] !== undefined;
        const isLastQuestion = this.currentQuestion === this.currentQuiz.questions.length - 1;
        
        if (isLastQuestion) {
            nextBtn.style.display = 'none';
            finishBtn.style.display = hasAnswer ? 'inline-flex' : 'none';
        } else {
            nextBtn.style.display = 'inline-flex';
            nextBtn.disabled = !hasAnswer;
            finishBtn.style.display = 'none';
        }
    }

    startTimer() {
        this.timer = setInterval(() => {
            this.timeLeft--;
            const timerEl = document.getElementById('quizTimer');
            if (timerEl) {
                timerEl.textContent = this.formatTime(this.timeLeft);
                
                // Changer la couleur quand il reste moins de 1 minute
                if (this.timeLeft < 60) {
                    timerEl.style.color = '#ef4444';
                }
            }
            
            if (this.timeLeft <= 0) {
                this.finishQuiz();
            }
        }, 1000);
    }

    finishQuiz() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        this.calculateScore();
        this.showResults();
        this.saveQuizResult();
        
        app.trackEvent('quiz_completed', { 
            quizId: this.currentQuiz.id, 
            score: this.score,
            timeUsed: this.currentQuiz.timeLimit - this.timeLeft
        });
    }

    calculateScore() {
        let correct = 0;
        this.currentQuiz.questions.forEach((question, index) => {
            if (this.answers[index] === question.correct) {
                correct++;
            }
        });
        this.score = Math.round((correct / this.currentQuiz.questions.length) * 100);
    }

    showResults() {
        const currentLang = app.currentLang;
        const quizContainer = document.querySelector('.quiz-container');
        const timeUsed = this.currentQuiz.timeLimit - this.timeLeft;
        
        let performance = '';
        if (this.score >= 90) performance = currentLang === 'fr' ? 'Excellent !' : 'ممتاز!';
        else if (this.score >= 70) performance = currentLang === 'fr' ? 'Très bien !' : 'جيد جداً!';
        else if (this.score >= 50) performance = currentLang === 'fr' ? 'Bien' : 'جيد';
        else performance = currentLang === 'fr' ? 'À améliorer' : 'يحتاج تحسين';
        
        quizContainer.innerHTML = `
            <div class="quiz-results">
                <div class="results-header">
                    <div class="score-circle">
                        <div class="score-number">${this.score}%</div>
                        <div class="score-label">${performance}</div>
                    </div>
                    <h2>${this.currentQuiz.title[currentLang]}</h2>
                </div>
                
                <div class="results-stats">
                    <div class="stat-item">
                        <i class="fas fa-check-circle"></i>
                        <span>${currentLang === 'fr' ? 'Bonnes réponses' : 'إجابات صحيحة'}: ${Math.round(this.score * this.currentQuiz.questions.length / 100)}/${this.currentQuiz.questions.length}</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>${currentLang === 'fr' ? 'Temps utilisé' : 'الوقت المستخدم'}: ${this.formatTime(timeUsed)}</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-trophy"></i>
                        <span>${currentLang === 'fr' ? 'Meilleur score' : 'أفضل نتيجة'}: ${this.getBestScore(this.currentQuiz.id)}%</span>
                    </div>
                </div>
                
                <div class="results-actions">
                    <button onclick="quizManager.reviewAnswers()" class="btn-secondary">
                        <i class="fas fa-eye"></i>
                        ${currentLang === 'fr' ? 'Revoir les réponses' : 'مراجعة الإجابات'}
                    </button>
                    <button onclick="quizManager.retakeQuiz()" class="btn-primary">
                        <i class="fas fa-redo"></i>
                        ${currentLang === 'fr' ? 'Refaire le quiz' : 'إعادة الاختبار'}
                    </button>
                    <button onclick="quizManager.loadQuizContent()" class="btn-success">
                        <i class="fas fa-list"></i>
                        ${currentLang === 'fr' ? 'Autres quiz' : 'اختبارات أخرى'}
                    </button>
                </div>
            </div>
        `;
    }

    reviewAnswers() {
        // Implémenter la revue des réponses
        console.log('Review answers functionality');
    }

    retakeQuiz() {
        this.startQuiz(this.currentQuiz.id);
    }

    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    getBestScore(quizId) {
        const saved = localStorage.getItem(`quiz_${quizId}_best`);
        return saved || 0;
    }

    getAttempts(quizId) {
        const saved = localStorage.getItem(`quiz_${quizId}_attempts`);
        return saved || 0;
    }

    saveQuizResult() {
        const quizId = this.currentQuiz.id;
        
        // Sauvegarder le meilleur score
        const currentBest = this.getBestScore(quizId);
        if (this.score > currentBest) {
            localStorage.setItem(`quiz_${quizId}_best`, this.score);
        }
        
        // Incrémenter le nombre de tentatives
        const attempts = this.getAttempts(quizId);
        localStorage.setItem(`quiz_${quizId}_attempts`, parseInt(attempts) + 1);
        
        // Sauvegarder dans le progrès global
        if (app.userProgress) {
            if (!app.userProgress.quizScores) app.userProgress.quizScores = {};
            app.userProgress.quizScores[quizId] = this.score;
            app.saveProgress();
        }
    }

    // Questions pour chaque quiz
    getBasicsQuestions() {
        return [
            {
                question: {
                    fr: "Qu'est-ce qu'un algorithme ?",
                    tn: "شنوة الخوارزمية؟"
                },
                options: [
                    { fr: "Un programme informatique", tn: "برنامج كمبيوتر" },
                    { fr: "Une suite d'instructions pour résoudre un problème", tn: "سلسلة من التعليمات لحل مشكلة" },
                    { fr: "Un langage de programmation", tn: "لغة برمجة" },
                    { fr: "Une base de données", tn: "قاعدة بيانات" }
                ],
                correct: 1,
                explanation: {
                    fr: "Un algorithme est une suite finie et non ambiguë d'instructions permettant de résoudre un problème.",
                    tn: "الخوارزمية هي سلسلة محدودة وواضحة من التعليمات لحل مشكلة."
                }
            }
            // Ajouter plus de questions...
        ];
    }

    getSortingQuestions() {
        return [
            {
                question: {
                    fr: "Quelle est la complexité temporelle moyenne de QuickSort ?",
                    tn: "شنوة التعقيد الزمني المتوسط لـ QuickSort؟"
                },
                options: [
                    { fr: "O(n)", tn: "O(n)" },
                    { fr: "O(n log n)", tn: "O(n log n)" },
                    { fr: "O(n²)", tn: "O(n²)" },
                    { fr: "O(log n)", tn: "O(log n)" }
                ],
                correct: 1
            }
        ];
    }

    getComplexityQuestions() {
        return [
            {
                question: {
                    fr: "Que signifie O(1) ?",
                    tn: "شنوة معنى O(1)؟"
                },
                options: [
                    { fr: "Complexité linéaire", tn: "تعقيد خطي" },
                    { fr: "Complexité constante", tn: "تعقيد ثابت" },
                    { fr: "Complexité logarithmique", tn: "تعقيد لوغاريتمي" },
                    { fr: "Complexité quadratique", tn: "تعقيد تربيعي" }
                ],
                correct: 1
            }
        ];
    }

    getDataStructuresQuestions() {
        return [
            {
                question: {
                    fr: "Quelle structure de données suit le principe LIFO ?",
                    tn: "أي هيكل بيانات يتبع مبدأ LIFO؟"
                },
                options: [
                    { fr: "File (Queue)", tn: "طابور (Queue)" },
                    { fr: "Pile (Stack)", tn: "كومة (Stack)" },
                    { fr: "Liste", tn: "قائمة" },
                    { fr: "Arbre", tn: "شجرة" }
                ],
                correct: 1
            }
        ];
    }

    getAdvancedQuestions() {
        return [
            {
                question: {
                    fr: "Quel algorithme utilise la programmation dynamique ?",
                    tn: "أي خوارزمية تستخدم البرمجة الديناميكية؟"
                },
                options: [
                    { fr: "BFS", tn: "BFS" },
                    { fr: "DFS", tn: "DFS" },
                    { fr: "Fibonacci optimisé", tn: "فيبوناتشي محسن" },
                    { fr: "QuickSort", tn: "QuickSort" }
                ],
                correct: 2
            }
        ];
    }
}

// Instance globale
const quizManager = new QuizManager();
