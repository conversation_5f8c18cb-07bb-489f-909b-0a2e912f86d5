// Gestionnaire d'exercices interactifs
class ExerciseManager {
    constructor() {
        this.exercises = {
            'algo-intro-ex1': {
                title: { 
                    fr: 'Trouver le maximum', 
                    ar: 'إيجاد القيمة العظمى' 
                },
                description: { 
                    fr: 'Écrivez un algorithme pour trouver le plus grand nombre dans une liste.',
                    ar: 'اكتب خوارزمية لإيجاد أكبر رقم في قائمة.'
                },
                initialCode: {
                    fr: `// Algorithme pour trouver le maximum
fonction trouverMaximum(liste):
    // Votre code ici
    
// Test avec la liste [3, 7, 2, 9, 1]
liste = [3, 7, 2, 9, 1]
resultat = trouverMaximum(liste)
afficher(resultat)`,
                    ar: `// خوارزمية لإيجاد القيمة العظمى
دالة إيجاد_الأعظم(قائمة):
    // كودك هنا
    
// اختبار مع القائمة [3, 7, 2, 9, 1]
قائمة = [3, 7, 2, 9, 1]
النتيجة = إيجاد_الأعظم(قائمة)
عرض(النتيجة)`
                },
                solution: {
                    fr: `fonction trouverMaximum(liste):
    si liste est vide:
        retourner null
    
    maximum = liste[0]
    pour i de 1 à longueur(liste) - 1:
        si liste[i] > maximum:
            maximum = liste[i]
    
    retourner maximum`,
                    ar: `دالة إيجاد_الأعظم(قائمة):
    إذا كانت القائمة فارغة:
        إرجاع فارغ
    
    الأعظم = قائمة[0]
    لكل i من 1 إلى طول(قائمة) - 1:
        إذا كان قائمة[i] > الأعظم:
            الأعظم = قائمة[i]
    
    إرجاع الأعظم`
                },
                hints: {
                    fr: [
                        'Commencez par initialiser une variable avec le premier élément',
                        'Parcourez la liste en comparant chaque élément',
                        'Mettez à jour la variable si vous trouvez un élément plus grand'
                    ],
                    ar: [
                        'ابدأ بتهيئة متغير بالعنصر الأول',
                        'تصفح القائمة مقارناً كل عنصر',
                        'حدث المتغير إذا وجدت عنصراً أكبر'
                    ]
                },
                testCases: [
                    { input: [3, 7, 2, 9, 1], expected: 9 },
                    { input: [1], expected: 1 },
                    { input: [-5, -2, -10, -1], expected: -1 },
                    { input: [5, 5, 5, 5], expected: 5 }
                ]
            },

            'algo-sort-ex1': {
                fr: {
                    title: "Tri par insertion",
                    description: "Implémentez l'algorithme de tri par insertion",
                    instructions: "Créez une fonction qui trie un tableau en utilisant l'algorithme de tri par insertion.",
                    starterCode: `def tri_insertion(tableau):
    # Votre code ici
    pass

# Test
tableau_test = [64, 34, 25, 12, 22, 11, 90]
print("Tableau original:", tableau_test)
resultat = tri_insertion(tableau_test.copy())
print("Tableau trié:", resultat)`,
                    solution: `def tri_insertion(tableau):
    for i in range(1, len(tableau)):
        cle = tableau[i]
        j = i - 1
        while j >= 0 and tableau[j] > cle:
            tableau[j + 1] = tableau[j]
            j -= 1
        tableau[j + 1] = cle
    return tableau`,
                    hints: [
                        "Commencez par le deuxième élément (index 1)",
                        "Comparez chaque élément avec ceux qui le précèdent",
                        "Décalez les éléments plus grands vers la droite"
                    ]
                },
                ar: {
                    title: "الترتيب بالإدراج",
                    description: "نفذ خوارزمية الترتيب بالإدراج",
                    instructions: "أنشئ دالة ترتب مصفوفة باستخدام خوارزمية الترتيب بالإدراج.",
                    starterCode: `def tri_insertion(tableau):
    # كودك هنا
    pass

# اختبار
tableau_test = [64, 34, 25, 12, 22, 11, 90]
print("المصفوفة الأصلية:", tableau_test)
resultat = tri_insertion(tableau_test.copy())
print("المصفوفة مرتبة:", resultat)`,
                    solution: `def tri_insertion(tableau):
    for i in range(1, len(tableau)):
        cle = tableau[i]
        j = i - 1
        while j >= 0 and tableau[j] > cle:
            tableau[j + 1] = tableau[j]
            j -= 1
        tableau[j + 1] = cle
    return tableau`,
                    hints: [
                        "ابدأ من العنصر الثاني (فهرس 1)",
                        "قارن كل عنصر مع العناصر التي تسبقه",
                        "انقل العناصر الأكبر نحو اليمين"
                    ]
                }
            },

            'algo-recursion-ex1': {
                fr: {
                    title: "Puissance récursive",
                    description: "Calculez x^n de manière récursive",
                    instructions: "Implémentez une fonction récursive pour calculer x élevé à la puissance n.",
                    starterCode: `def puissance(x, n):
    # Votre code ici
    pass

# Tests
print("2^3 =", puissance(2, 3))
print("5^0 =", puissance(5, 0))
print("3^4 =", puissance(3, 4))`,
                    solution: `def puissance(x, n):
    # Cas de base
    if n == 0:
        return 1
    # Cas récursif
    else:
        return x * puissance(x, n - 1)`,
                    hints: [
                        "Cas de base : x^0 = 1",
                        "Cas récursif : x^n = x * x^(n-1)",
                        "Assurez-vous que n diminue à chaque appel"
                    ]
                },
                ar: {
                    title: "القوة العودية",
                    description: "احسب x^n بطريقة عودية",
                    instructions: "نفذ دالة عودية لحساب x مرفوع للقوة n.",
                    starterCode: `def puissance(x, n):
    # كودك هنا
    pass

# اختبارات
print("2^3 =", puissance(2, 3))
print("5^0 =", puissance(5, 0))
print("3^4 =", puissance(3, 4))`,
                    solution: `def puissance(x, n):
    # الحالة الأساسية
    if n == 0:
        return 1
    # الحالة العودية
    else:
        return x * puissance(x, n - 1)`,
                    hints: [
                        "الحالة الأساسية: x^0 = 1",
                        "الحالة العودية: x^n = x * x^(n-1)",
                        "تأكد أن n تقل في كل استدعاء"
                    ]
                }
            },

            'python-basics-ex1': {
                fr: {
                    title: "Classification d'âge",
                    description: "Classifiez l'âge d'une personne",
                    instructions: "Créez un programme qui demande l'âge et affiche la catégorie (mineur, majeur, senior).",
                    starterCode: `# Demander l'âge à l'utilisateur
age = int(input("Entrez votre âge: "))

# Votre code ici pour classifier l'âge
`,
                    solution: `age = int(input("Entrez votre âge: "))

if age < 18:
    print("Vous êtes mineur")
elif age < 65:
    print("Vous êtes majeur")
else:
    print("Vous êtes senior")`,
                    hints: [
                        "Utilisez des conditions if/elif/else",
                        "Mineur : < 18 ans",
                        "Senior : >= 65 ans"
                    ]
                },
                ar: {
                    title: "تصنيف العمر",
                    description: "صنف عمر الشخص",
                    instructions: "أنشئ برنامجاً يطلب العمر ويعرض الفئة (قاصر، بالغ، كبير السن).",
                    starterCode: `# اطلب العمر من المستخدم
age = int(input("أدخل عمرك: "))

# كودك هنا لتصنيف العمر
`,
                    solution: `age = int(input("أدخل عمرك: "))

if age < 18:
    print("أنت قاصر")
elif age < 65:
    print("أنت بالغ")
else:
    print("أنت كبير السن")`,
                    hints: [
                        "استخدم شروط if/elif/else",
                        "قاصر: < 18 سنة",
                        "كبير السن: >= 65 سنة"
                    ]
                }
            }
        };

        this.currentExercise = null;
        this.currentHintIndex = 0;
    }

    openExercise(exerciseId) {
        const exercise = this.exercises[exerciseId];
        if (!exercise) {
            console.error('Exercise not found:', exerciseId);
            return;
        }

        this.currentExercise = exerciseId;
        this.currentHintIndex = 0;
        
        const currentLang = window.app ? window.app.currentLanguage : 'fr';
        
        // Create exercise modal
        const modal = this.createExerciseModal(exercise, currentLang);
        document.body.appendChild(modal);
        
        // Show modal
        setTimeout(() => {
            modal.classList.add('active');
        }, 100);
    }

    createExerciseModal(exercise, lang) {
        const modal = document.createElement('div');
        modal.className = 'modal exercise-modal';
        modal.id = 'exerciseModal';
        
        modal.innerHTML = `
            <div class="modal-content exercise-content">
                <div class="modal-header">
                    <h3>${exercise.title[lang]}</h3>
                    <button class="modal-close" onclick="this.closeExercise()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="exercise-layout">
                        <div class="exercise-instructions">
                            <h4 data-fr="Instructions" data-ar="التعليمات">${lang === 'fr' ? 'Instructions' : 'التعليمات'}</h4>
                            <p>${exercise.description[lang]}</p>
                            
                            <div class="exercise-hints">
                                <h5 data-fr="Indices" data-ar="تلميحات">${lang === 'fr' ? 'Indices' : 'تلميحات'}</h5>
                                <div class="hints-container" id="hintsContainer">
                                    <button class="btn-hint" onclick="window.ExerciseManager.showNextHint()">
                                        <i class="fas fa-lightbulb"></i>
                                        <span data-fr="Afficher un indice" data-ar="إظهار تلميح">${lang === 'fr' ? 'Afficher un indice' : 'إظهار تلميح'}</span>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="exercise-actions">
                                <button class="btn-primary" onclick="window.ExerciseManager.checkSolution()">
                                    <i class="fas fa-check"></i>
                                    <span data-fr="Vérifier" data-ar="تحقق">${lang === 'fr' ? 'Vérifier' : 'تحقق'}</span>
                                </button>
                                <button class="btn-secondary" onclick="window.ExerciseManager.showSolution()">
                                    <i class="fas fa-eye"></i>
                                    <span data-fr="Voir la solution" data-ar="عرض الحل">${lang === 'fr' ? 'Voir la solution' : 'عرض الحل'}</span>
                                </button>
                                <button class="btn-secondary" onclick="window.ExerciseManager.resetExercise()">
                                    <i class="fas fa-undo"></i>
                                    <span data-fr="Réinitialiser" data-ar="إعادة تعيين">${lang === 'fr' ? 'Réinitialiser' : 'إعادة تعيين'}</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="exercise-editor">
                            <div class="editor-header">
                                <h4 data-fr="Votre solution" data-ar="حلك">${lang === 'fr' ? 'Votre solution' : 'حلك'}</h4>
                            </div>
                            <textarea id="exerciseCode" class="exercise-code-editor">${exercise.initialCode[lang]}</textarea>
                            
                            <div class="exercise-output">
                                <h5 data-fr="Résultats des tests" data-ar="نتائج الاختبارات">${lang === 'fr' ? 'Résultats des tests' : 'نتائج الاختبارات'}</h5>
                                <div id="testResults" class="test-results"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return modal;
    }

    closeExercise() {
        const modal = document.getElementById('exerciseModal');
        if (modal) {
            modal.classList.remove('active');
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        }
    }

    showNextHint() {
        const exercise = this.exercises[this.currentExercise];
        const currentLang = window.app ? window.app.currentLanguage : 'fr';
        const hints = exercise.hints[currentLang];
        
        if (this.currentHintIndex < hints.length) {
            const hintsContainer = document.getElementById('hintsContainer');
            const hintElement = document.createElement('div');
            hintElement.className = 'hint-item';
            hintElement.innerHTML = `
                <i class="fas fa-lightbulb"></i>
                <span>${hints[this.currentHintIndex]}</span>
            `;
            
            // Insert before the button
            const button = hintsContainer.querySelector('.btn-hint');
            hintsContainer.insertBefore(hintElement, button);
            
            this.currentHintIndex++;
            
            // Hide button if no more hints
            if (this.currentHintIndex >= hints.length) {
                button.style.display = 'none';
            }
        }
    }

    checkSolution() {
        const code = document.getElementById('exerciseCode').value;
        const exercise = this.exercises[this.currentExercise];
        const currentLang = window.app ? window.app.currentLanguage : 'fr';
        
        // Simple validation - in a real app, you'd have a proper code execution environment
        const results = this.validateSolution(code, exercise.testCases);
        this.displayTestResults(results, currentLang);
        
        if (results.allPassed) {
            this.showSuccessMessage(currentLang);
        }
    }

    validateSolution(code, testCases) {
        // This is a simplified validation
        // In a real application, you would use a proper code execution sandbox
        const results = {
            allPassed: false,
            testResults: []
        };
        
        // Mock validation - check if code contains key elements
        const hasLoop = /pour|for|while|tant que/i.test(code);
        const hasComparison = />/i.test(code);
        const hasVariable = /maximum|max|plus grand|أعظم|أكبر/i.test(code);
        
        const passed = hasLoop && hasComparison && hasVariable;
        
        testCases.forEach((testCase, index) => {
            results.testResults.push({
                input: testCase.input,
                expected: testCase.expected,
                actual: passed ? testCase.expected : 'Erreur',
                passed: passed
            });
        });
        
        results.allPassed = results.testResults.every(result => result.passed);
        
        return results;
    }

    displayTestResults(results, lang) {
        const container = document.getElementById('testResults');
        container.innerHTML = '';
        
        results.testResults.forEach((result, index) => {
            const resultElement = document.createElement('div');
            resultElement.className = `test-result ${result.passed ? 'passed' : 'failed'}`;
            
            resultElement.innerHTML = `
                <div class="test-header">
                    <span class="test-number">${lang === 'fr' ? 'Test' : 'اختبار'} ${index + 1}</span>
                    <span class="test-status">
                        <i class="fas ${result.passed ? 'fa-check' : 'fa-times'}"></i>
                        ${result.passed ? (lang === 'fr' ? 'Réussi' : 'نجح') : (lang === 'fr' ? 'Échoué' : 'فشل')}
                    </span>
                </div>
                <div class="test-details">
                    <div><strong>${lang === 'fr' ? 'Entrée' : 'المدخل'}:</strong> [${result.input.join(', ')}]</div>
                    <div><strong>${lang === 'fr' ? 'Attendu' : 'المتوقع'}:</strong> ${result.expected}</div>
                    <div><strong>${lang === 'fr' ? 'Obtenu' : 'المحصل'}:</strong> ${result.actual}</div>
                </div>
            `;
            
            container.appendChild(resultElement);
        });
    }

    showSolution() {
        const exercise = this.exercises[this.currentExercise];
        const currentLang = window.app ? window.app.currentLanguage : 'fr';
        const codeEditor = document.getElementById('exerciseCode');
        
        codeEditor.value = exercise.solution[currentLang];
        
        // Show notification
        if (window.app) {
            window.app.showNotification(
                currentLang === 'fr' ? 'Solution affichée' : 'تم عرض الحل',
                'info'
            );
        }
    }

    resetExercise() {
        const exercise = this.exercises[this.currentExercise];
        const currentLang = window.app ? window.app.currentLanguage : 'fr';
        const codeEditor = document.getElementById('exerciseCode');
        
        codeEditor.value = exercise.initialCode[currentLang];
        
        // Reset hints
        this.currentHintIndex = 0;
        const hintsContainer = document.getElementById('hintsContainer');
        hintsContainer.innerHTML = `
            <button class="btn-hint" onclick="window.ExerciseManager.showNextHint()">
                <i class="fas fa-lightbulb"></i>
                <span data-fr="Afficher un indice" data-ar="إظهار تلميح">${currentLang === 'fr' ? 'Afficher un indice' : 'إظهار تلميح'}</span>
            </button>
        `;
        
        // Clear test results
        document.getElementById('testResults').innerHTML = '';
    }

    showSuccessMessage(lang) {
        if (window.app) {
            window.app.showNotification(
                lang === 'fr' ? 'Félicitations ! Exercice réussi !' : 'تهانينا! تم إنجاز التمرين بنجاح!',
                'success'
            );
        }
        
        // Mark exercise as completed (you could extend this)
        setTimeout(() => {
            this.closeExercise();
        }, 2000);
    }
}

// Initialize exercise manager
window.ExerciseManager = new ExerciseManager();
