// Système de jeux interactifs
class GameManager {
    constructor() {
        this.games = this.initGames();
        this.currentGame = null;
    }

    initGames() {
        return {
            sortingGame: {
                id: 'sorting',
                title: { fr: 'Jeu de <PERSON>', tn: 'لعبة الترتيب' },
                description: { 
                    fr: 'Trie les nombres dans l\'ordre croissant le plus rapidement possible !', 
                    tn: 'رتب الأرقام بالترتيب التصاعدي بأسرع ما يمكن!' 
                },
                difficulty: { fr: 'Facile', tn: 'سهل' },
                icon: 'fas fa-sort-numeric-up',
                bestTime: null,
                create: () => this.createSortingGame()
            },
            
            memoryGame: {
                id: 'memory',
                title: { fr: 'Mémoire Algorithmique', tn: 'ذاكرة الخوارزميات' },
                description: { 
                    fr: 'Mémorise la séquence d\'algorithmes et reproduis-la !', 
                    tn: 'احفظ تسلسل الخوارزميات وأعد إنتاجها!' 
                },
                difficulty: { fr: 'Moyen', tn: 'متوسط' },
                icon: 'fas fa-brain',
                bestScore: null,
                create: () => this.createMemoryGame()
            },
            
            pathfindingGame: {
                id: 'pathfinding',
                title: { fr: 'Trouve le Chemin', tn: 'اعثر على الطريق' },
                description: { 
                    fr: 'Guide le robot vers la sortie en évitant les obstacles !', 
                    tn: 'وجه الروبوت نحو المخرج مع تجنب العوائق!' 
                },
                difficulty: { fr: 'Difficile', tn: 'صعب' },
                icon: 'fas fa-route',
                bestMoves: null,
                create: () => this.createPathfindingGame()
            },
            
            binaryGame: {
                id: 'binary',
                title: { fr: 'Binaire Express', tn: 'الثنائي السريع' },
                description: { 
                    fr: 'Convertis les nombres décimaux en binaire rapidement !', 
                    tn: 'حول الأرقام العشرية إلى ثنائية بسرعة!' 
                },
                difficulty: { fr: 'Moyen', tn: 'متوسط' },
                icon: 'fas fa-code',
                bestScore: null,
                create: () => this.createBinaryGame()
            },
            
            algorithmRace: {
                id: 'race',
                title: { fr: 'Course d\'Algorithmes', tn: 'سباق الخوارزميات' },
                description: { 
                    fr: 'Choisis le bon algorithme pour chaque problème !', 
                    tn: 'اختر الخوارزمية الصحيحة لكل مشكلة!' 
                },
                difficulty: { fr: 'Expert', tn: 'خبير' },
                icon: 'fas fa-trophy',
                bestTime: null,
                create: () => this.createAlgorithmRaceGame()
            }
        };
    }

    loadGamesContent() {
        const gamesGrid = document.querySelector('.games-grid');
        if (!gamesGrid) return;

        gamesGrid.innerHTML = '';
        
        Object.values(this.games).forEach(game => {
            const gameCard = this.createGameCard(game);
            gamesGrid.appendChild(gameCard);
        });
    }

    createGameCard(game) {
        const currentLang = app.currentLang;
        const card = document.createElement('div');
        card.className = 'game-card hover-lift';
        card.onclick = () => this.startGame(game.id);

        card.innerHTML = `
            <div class="game-header">
                <div class="game-icon">
                    <i class="${game.icon}"></i>
                </div>
                <h3 class="game-title">${game.title[currentLang]}</h3>
                <span class="game-difficulty">${game.difficulty[currentLang]}</span>
            </div>
            <div class="game-body">
                <p class="game-description">${game.description[currentLang]}</p>
                <div class="game-stats">
                    <span>${currentLang === 'fr' ? 'Meilleur score' : 'أفضل نتيجة'}: ${this.getBestScore(game)}</span>
                    <span>${currentLang === 'fr' ? 'Joué' : 'لعبت'}: ${this.getPlayCount(game)} ${currentLang === 'fr' ? 'fois' : 'مرة'}</span>
                </div>
                <button class="game-play-btn">
                    <i class="fas fa-play"></i>
                    ${currentLang === 'fr' ? 'Jouer' : 'العب'}
                </button>
            </div>
        `;

        return card;
    }

    startGame(gameId) {
        const game = this.games[gameId];
        if (!game) return;

        this.currentGame = game;
        const gameContent = game.create();
        const currentLang = app.currentLang;
        
        app.showModal('gameModal', game.title[currentLang], gameContent);
        app.trackEvent('game_started', { gameId });
    }

    // Jeu de tri
    createSortingGame() {
        const numbers = this.generateRandomNumbers(8, 1, 50);
        let startTime = Date.now();
        let moves = 0;

        const gameHTML = `
            <div class="sorting-game">
                <div class="game-info">
                    <div class="timer">⏱️ <span id="timer">00:00</span></div>
                    <div class="moves">🔄 <span id="moves">0</span> ${app.currentLang === 'fr' ? 'mouvements' : 'حركة'}</div>
                </div>
                <div class="sorting-container" id="sortingContainer">
                    ${numbers.map((num, index) => 
                        `<div class="sortable-item" data-value="${num}" data-index="${index}">${num}</div>`
                    ).join('')}
                </div>
                <div class="game-controls">
                    <button onclick="gameManager.resetSortingGame()" class="btn-secondary">
                        ${app.currentLang === 'fr' ? 'Recommencer' : 'ابدأ من جديد'}
                    </button>
                    <button onclick="gameManager.checkSortingResult()" class="btn-primary">
                        ${app.currentLang === 'fr' ? 'Vérifier' : 'تحقق'}
                    </button>
                </div>
            </div>
        `;

        setTimeout(() => {
            this.initSortingGameLogic();
            this.startTimer();
        }, 100);

        return gameHTML;
    }

    initSortingGameLogic() {
        const container = document.getElementById('sortingContainer');
        if (!container) return;

        let draggedElement = null;

        container.addEventListener('dragstart', (e) => {
            draggedElement = e.target;
            e.target.style.opacity = '0.5';
        });

        container.addEventListener('dragend', (e) => {
            e.target.style.opacity = '1';
        });

        container.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        container.addEventListener('drop', (e) => {
            e.preventDefault();
            if (e.target.classList.contains('sortable-item') && e.target !== draggedElement) {
                const temp = draggedElement.textContent;
                const tempValue = draggedElement.getAttribute('data-value');
                
                draggedElement.textContent = e.target.textContent;
                draggedElement.setAttribute('data-value', e.target.getAttribute('data-value'));
                
                e.target.textContent = temp;
                e.target.setAttribute('data-value', tempValue);
                
                this.incrementMoves();
            }
        });

        // Rendre les éléments draggables
        container.querySelectorAll('.sortable-item').forEach(item => {
            item.draggable = true;
        });
    }

    // Jeu de mémoire
    createMemoryGame() {
        const algorithms = ['BFS', 'DFS', 'Dijkstra', 'A*', 'QuickSort', 'MergeSort'];
        const sequence = this.generateSequence(algorithms, 4);
        let playerSequence = [];
        let showingSequence = true;

        const gameHTML = `
            <div class="memory-game">
                <div class="game-info">
                    <div class="level">📊 ${app.currentLang === 'fr' ? 'Niveau' : 'مستوى'}: <span id="level">1</span></div>
                    <div class="score">⭐ ${app.currentLang === 'fr' ? 'Score' : 'نقاط'}: <span id="score">0</span></div>
                </div>
                <div class="sequence-display" id="sequenceDisplay">
                    ${app.currentLang === 'fr' ? 'Mémorisez la séquence...' : 'احفظ التسلسل...'}
                </div>
                <div class="algorithm-buttons" id="algorithmButtons">
                    ${algorithms.map(algo => 
                        `<button class="algo-btn" data-algo="${algo}" onclick="gameManager.selectAlgorithm('${algo}')">${algo}</button>`
                    ).join('')}
                </div>
                <div class="game-controls">
                    <button onclick="gameManager.startMemorySequence()" class="btn-primary">
                        ${app.currentLang === 'fr' ? 'Commencer' : 'ابدأ'}
                    </button>
                </div>
            </div>
        `;

        return gameHTML;
    }

    // Jeu de recherche de chemin
    createPathfindingGame() {
        const gridSize = 8;
        const grid = this.generateMaze(gridSize);

        const gameHTML = `
            <div class="pathfinding-game">
                <div class="game-info">
                    <div class="moves">👣 ${app.currentLang === 'fr' ? 'Mouvements' : 'حركات'}: <span id="pathMoves">0</span></div>
                    <div class="hint">${app.currentLang === 'fr' ? 'Utilisez les flèches pour déplacer le robot' : 'استخدم الأسهم لتحريك الروبوت'}</div>
                </div>
                <div class="maze-grid" id="mazeGrid">
                    ${this.renderMaze(grid)}
                </div>
                <div class="game-controls">
                    <button onclick="gameManager.resetPathfinding()" class="btn-secondary">
                        ${app.currentLang === 'fr' ? 'Recommencer' : 'ابدأ من جديد'}
                    </button>
                    <button onclick="gameManager.showPathHint()" class="btn-primary">
                        ${app.currentLang === 'fr' ? 'Indice' : 'تلميح'}
                    </button>
                </div>
            </div>
        `;

        setTimeout(() => {
            this.initPathfindingControls();
        }, 100);

        return gameHTML;
    }

    // Jeu binaire
    createBinaryGame() {
        const targetNumber = Math.floor(Math.random() * 255) + 1;
        let score = 0;
        let timeLeft = 60;

        const gameHTML = `
            <div class="binary-game">
                <div class="game-info">
                    <div class="target">🎯 ${app.currentLang === 'fr' ? 'Nombre' : 'رقم'}: <span id="targetNumber">${targetNumber}</span></div>
                    <div class="timer">⏰ <span id="binaryTimer">${timeLeft}s</span></div>
                    <div class="score">⭐ <span id="binaryScore">${score}</span></div>
                </div>
                <div class="binary-input">
                    <input type="text" id="binaryInput" placeholder="${app.currentLang === 'fr' ? 'Entrez le binaire...' : 'أدخل الثنائي...'}" maxlength="8">
                    <button onclick="gameManager.checkBinaryAnswer()" class="btn-primary">
                        ${app.currentLang === 'fr' ? 'Vérifier' : 'تحقق'}
                    </button>
                </div>
                <div class="binary-help">
                    <h4>${app.currentLang === 'fr' ? 'Aide' : 'مساعدة'}:</h4>
                    <div class="binary-chart">
                        <div class="bit-position">128 64 32 16 8 4 2 1</div>
                        <div class="bit-values">
                            ${Array.from({length: 8}, (_, i) => 
                                `<input type="checkbox" onchange="gameManager.updateBinaryHelper()" data-value="${Math.pow(2, 7-i)}">`
                            ).join('')}
                        </div>
                    </div>
                    <div class="helper-result">= <span id="helperResult">0</span></div>
                </div>
            </div>
        `;

        setTimeout(() => {
            this.startBinaryTimer();
        }, 100);

        return gameHTML;
    }

    // Course d'algorithmes
    createAlgorithmRaceGame() {
        const problems = this.getAlgorithmProblems();
        let currentProblem = 0;
        let score = 0;
        let startTime = Date.now();

        const gameHTML = `
            <div class="algorithm-race">
                <div class="game-info">
                    <div class="progress">📈 ${currentProblem + 1}/${problems.length}</div>
                    <div class="score">⭐ <span id="raceScore">${score}</span></div>
                    <div class="timer">⏱️ <span id="raceTimer">00:00</span></div>
                </div>
                <div class="problem-container" id="problemContainer">
                    ${this.renderProblem(problems[currentProblem])}
                </div>
                <div class="algorithm-choices" id="algorithmChoices">
                    ${this.renderAlgorithmChoices(problems[currentProblem])}
                </div>
            </div>
        `;

        setTimeout(() => {
            this.startRaceTimer();
        }, 100);

        return gameHTML;
    }

    // Fonctions utilitaires
    generateRandomNumbers(count, min, max) {
        const numbers = [];
        for (let i = 0; i < count; i++) {
            numbers.push(Math.floor(Math.random() * (max - min + 1)) + min);
        }
        return numbers;
    }

    generateSequence(items, length) {
        const sequence = [];
        for (let i = 0; i < length; i++) {
            sequence.push(items[Math.floor(Math.random() * items.length)]);
        }
        return sequence;
    }

    generateMaze(size) {
        const maze = Array(size).fill().map(() => Array(size).fill(0));
        
        // Ajouter des obstacles aléatoires
        for (let i = 0; i < size * size * 0.3; i++) {
            const x = Math.floor(Math.random() * size);
            const y = Math.floor(Math.random() * size);
            if ((x !== 0 || y !== 0) && (x !== size-1 || y !== size-1)) {
                maze[x][y] = 1;
            }
        }
        
        return maze;
    }

    renderMaze(grid) {
        return grid.map((row, i) => 
            row.map((cell, j) => {
                let className = 'maze-cell';
                if (i === 0 && j === 0) className += ' robot';
                else if (i === grid.length-1 && j === row.length-1) className += ' exit';
                else if (cell === 1) className += ' wall';
                
                return `<div class="${className}" data-x="${i}" data-y="${j}"></div>`;
            }).join('')
        ).join('');
    }

    getBestScore(game) {
        const saved = localStorage.getItem(`game_${game.id}_best`);
        return saved || (app.currentLang === 'fr' ? 'Aucun' : 'لا يوجد');
    }

    getPlayCount(game) {
        const saved = localStorage.getItem(`game_${game.id}_count`);
        return saved || 0;
    }

    incrementMoves() {
        const movesEl = document.getElementById('moves');
        if (movesEl) {
            const current = parseInt(movesEl.textContent) + 1;
            movesEl.textContent = current;
        }
    }

    startTimer() {
        const timerEl = document.getElementById('timer');
        if (!timerEl) return;

        const startTime = Date.now();
        const timer = setInterval(() => {
            const elapsed = Date.now() - startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            timerEl.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);

        // Sauvegarder la référence du timer pour pouvoir l'arrêter
        this.currentTimer = timer;
    }

    getAlgorithmProblems() {
        return [
            {
                problem: { 
                    fr: 'Trier un tableau de 1000 éléments', 
                    tn: 'ترتيب مصفوفة من 1000 عنصر' 
                },
                correct: 'QuickSort',
                options: ['BubbleSort', 'QuickSort', 'LinearSearch', 'BFS']
            },
            {
                problem: { 
                    fr: 'Trouver le chemin le plus court dans un graphe', 
                    tn: 'العثور على أقصر مسار في الرسم البياني' 
                },
                correct: 'Dijkstra',
                options: ['DFS', 'BFS', 'Dijkstra', 'QuickSort']
            }
        ];
    }

    renderProblem(problem) {
        const currentLang = app.currentLang;
        return `<div class="problem-text">${problem.problem[currentLang]}</div>`;
    }

    renderAlgorithmChoices(problem) {
        return problem.options.map(option => 
            `<button class="choice-btn" onclick="gameManager.selectAlgorithmChoice('${option}', '${problem.correct}')">${option}</button>`
        ).join('');
    }
}

// Instance globale
const gameManager = new GameManager();
